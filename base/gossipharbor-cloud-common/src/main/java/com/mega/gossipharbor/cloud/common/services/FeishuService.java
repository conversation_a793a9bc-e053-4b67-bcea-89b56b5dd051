package com.mega.gossipharbor.cloud.common.services;

import com.mega.gossipharbor.cloud.common.dto.common.FeishuMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
@Service
@Slf4j
public class FeishuService {
    private final RestTemplate restTemplate;
    @Autowired
    public FeishuService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Async("taskExecutor")
    public void sendRobotMsgByMarkDownAsync(String title, String text, String botPath) {
        FeishuMessage feishuMessage = new FeishuMessage();
        FeishuMessage.Card.Elements element = new FeishuMessage.Card.Elements();
        FeishuMessage.Card.Elements.Text elementText = new FeishuMessage.Card.Elements.Text();
        elementText.setContent(text);
        element.setText(elementText);

        FeishuMessage.Card card = new FeishuMessage.Card();
        List<FeishuMessage.Card.Elements> elements = new ArrayList<>();
        elements.add(element);
        card.setElements(elements);

        FeishuMessage.Card.Header header = new FeishuMessage.Card.Header();
        FeishuMessage.Card.Header.Title headerTitle = new FeishuMessage.Card.Header.Title();
        headerTitle.setContent(title);
        header.setTitle(headerTitle);
        card.setHeader(header);
        feishuMessage.setCard(card);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<FeishuMessage> requestEntity = new HttpEntity<>(feishuMessage, headers);
        String result = this.restTemplate.postForObject(botPath, requestEntity, String.class);
        log.info("result:" + result);
    }
}
