package com.mega.gossipharbor.cloud.common.utils;

import com.mega.gossipharbor.cloud.common.config.JwtProperties;
import com.mega.gossipharbor.cloud.common.constant.JwtConstants;
import com.mega.gossipharbor.cloud.common.vo.BaseAccountReqVO;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;

@Component
public class JwtTokenUtil {
    private final JwtProperties jwtProperties;

    public JwtTokenUtil(JwtProperties jwtProperties) {
        this.jwtProperties = jwtProperties;
    }

    public String generateToken(BaseAccountReqVO baseReqVO) {
        return Jwts.builder()
                .claim(JwtConstants.CLAIM_ACCOUNT_ID, baseReqVO.getAccountId())
                .claim(JwtConstants.CLAIM_CHARACTER_ID, baseReqVO.getCharacterId())
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + jwtProperties.getExpireSeconds() * 1000))
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    private Key getSigningKey() {
        byte[] keyBytes = jwtProperties.getSecret().getBytes(StandardCharsets.UTF_8);
        return Keys.hmacShaKeyFor(keyBytes);
    }


    public BaseAccountReqVO parseToken(String token) {
        Claims claims = Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();

        BaseAccountReqVO baseReqVO = new BaseAccountReqVO();
        baseReqVO.setAccountId(claims.get(JwtConstants.CLAIM_ACCOUNT_ID, Long.class));
        baseReqVO.setCharacterId(claims.get(JwtConstants.CLAIM_CHARACTER_ID, Long.class));
        return baseReqVO;
    }
}
