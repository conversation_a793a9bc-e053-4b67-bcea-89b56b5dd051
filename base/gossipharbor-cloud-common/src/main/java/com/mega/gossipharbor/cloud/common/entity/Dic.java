package com.mega.gossipharbor.cloud.common.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "dic")
public class Dic {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 字典含义
     */
    @Column(name = "name")
    private String name;

    /**
     * 字典值
     */
    @Column(name = "value")
    private String value;

    /**
     * 字典类别ID
     */
    @Column(name = "common_dic_cate_id")
    private Long commonDicCateId;

    /**
     * 排序
     */
    @Column(name = "sort")
    private Integer sort;

    /**
     * 描述
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}