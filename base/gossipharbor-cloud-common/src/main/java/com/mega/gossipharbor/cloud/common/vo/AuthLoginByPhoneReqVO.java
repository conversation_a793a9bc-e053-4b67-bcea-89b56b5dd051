package com.mega.gossipharbor.cloud.common.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class AuthLoginByPhoneReqVO {
    @NotBlank
    private String phone; // 不带区号，如 13888888888

    @NotBlank
    private String areaCode; // 区号，如 86

    @NotBlank
    private String verificationCode; // 验证码

    @NotBlank
    private String clientIp;

    @NotBlank
    private String deviceId;
}
