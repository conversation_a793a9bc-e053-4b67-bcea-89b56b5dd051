package com.mega.gossipharbor.cloud.common;

import com.mega.gossipharbor.cloud.common.aop.RedisConsumeManager;
import com.mega.gossipharbor.cloud.common.aop.SqlExecuteTimeInterceptor;
import com.mega.gossipharbor.cloud.core.ds.DynamicDataSource;
import com.mega.gossipharbor.cloud.core.filter.TraceIdFilter;
import com.zaxxer.hikari.HikariDataSource;
import feign.Retryer;
import io.lettuce.core.ClientOptions;
import io.lettuce.core.TimeoutOptions;
import io.lettuce.core.cluster.ClusterClientOptions;
import io.lettuce.core.cluster.ClusterTopologyRefreshOptions;
import io.lettuce.core.resource.ClientResources;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.ibatis.plugin.Interceptor;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.data.redis.connection.*;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@EnableAsync
@EnableScheduling
@Configuration
@EnableFeignClients
@Order(-2)
public class CommonConfig implements SchedulingConfigurer {

    public static String ENV;

    @Bean
    public Retryer retryer() {
        return new Retryer.Default();
    }

    @Bean
    public RedisConsumeManager redisConsumeManager() {
        return new RedisConsumeManager();
    }

    @Bean
    public ThreadPoolExecutor scheduledTaskExecutor() {
        int nThreads = Runtime.getRuntime().availableProcessors() * 2;
        return new ScheduledThreadPoolExecutor(nThreads);
    }

    @Bean
    public ThreadPoolExecutor taskExecutor() {
        int nThreads = Runtime.getRuntime().availableProcessors() * 2;
        return new ThreadPoolExecutor(nThreads, nThreads, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(256), new ThreadPoolExecutor.AbortPolicy());
    }

    @Bean
    public FilterRegistrationBean<TraceIdFilter> filterRegistrationBean() {
        FilterRegistrationBean<TraceIdFilter> bean = new FilterRegistrationBean<>();
        bean.setFilter(new TraceIdFilter());
        bean.addUrlPatterns("/*");
        return bean;
    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setScheduler(scheduledTaskExecutor());
    }

    @Value("${spring.profiles.active}")
    public void setActiveEnv(String active) {
        ENV = active;
    }

    @Configuration
    static class DynamicDataSourceConfig {

        @Autowired
        SqlExecuteTimeInterceptor sqlExecuteTimeInterceptor;
        @Bean("dynamicDataSource")
        public DataSource dynamicDataSource(@Qualifier("master") HikariDataSource master,
                                            @Autowired(required = false) @Qualifier("slave") HikariDataSource slave,
                                            @Autowired(required = false) @Qualifier("clickhouse") HikariDataSource clickhouse,
                                            @Autowired(required = false) @Qualifier("gameMaster") HikariDataSource gameMaster,
                                            @Autowired(required = false) @Qualifier("gameSlave") HikariDataSource gameSlave) {
            DynamicDataSource dynamicDataSource = new DynamicDataSource();
            Map<Object, Object> dataSourceMap = new HashMap<>();
            dataSourceMap.put("master", master);
            if (slave != null) {
                dataSourceMap.put("slave", slave);
            }
            if (clickhouse != null) {
                dataSourceMap.put("clickhouse", clickhouse);
            }
            if (gameMaster != null) {
                dataSourceMap.put("gameMaster", gameMaster);
            }
            if (gameSlave != null) {
                dataSourceMap.put("gameSlave", gameSlave);
            }
            dynamicDataSource.setDefaultTargetDataSource(master);
            dynamicDataSource.setTargetDataSources(dataSourceMap);
            return dynamicDataSource;
        }

        @Bean
        @ConfigurationProperties(prefix = "mybatis.configuration")
        public org.apache.ibatis.session.Configuration mybatisConfiguration() {
            return new org.apache.ibatis.session.Configuration();
        }

        @Bean
        public SqlSessionFactoryBean sqlSessionFactoryBean(@Qualifier("dynamicDataSource") DataSource dynamicDataSource) throws IOException {
            SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
            sessionFactory.setDataSource(dynamicDataSource);
            sessionFactory.setConfiguration(mybatisConfiguration());
            sessionFactory.setPlugins(new Interceptor[]{sqlExecuteTimeInterceptor});
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            sessionFactory.setMapperLocations(resolver.getResources("classpath*:mapper/**/*.xml"));
            System.out.println("MapperLocations: " + Arrays.toString(resolver.getResources("classpath*:mapper/**/*.xml")) + " 个 XML 文件加载");
            return sessionFactory;
        }

        @Bean
        public DataSourceTransactionManager transactionManager(DataSource dynamicDataSource) {
            return new DataSourceTransactionManager(dynamicDataSource);
        }
    }

    @Configuration
    static class DataSourceMasterConfig {

        @Bean("master")
        @ConfigurationProperties(prefix = "spring.datasource.master")
        public HikariDataSource master() {
            return (HikariDataSource) DataSourceBuilder.create().build();
        }
    }

    @Configuration
    @ConditionalOnProperty(prefix = "spring.datasource.slave", name = "enabled", havingValue = "true")
    static class DataSourceSlaveConfig {

        @Bean("slave")
        @ConfigurationProperties(prefix = "spring.datasource.slave")
        public HikariDataSource slave() {
            return (HikariDataSource) DataSourceBuilder.create().build();
        }
    }

    @Configuration
    @ConditionalOnProperty(prefix = "spring.datasource.game-master", name = "enabled", havingValue = "true")
    static class DataSourceGameMasterConfig {

        @Bean("gameMaster")
        @ConfigurationProperties(prefix = "spring.datasource.game-master")
        public HikariDataSource gameMaster() {
            return (HikariDataSource) DataSourceBuilder.create().build();
        }
    }

    @Configuration
    @ConditionalOnProperty(prefix = "spring.datasource.game-slave", name = "enabled", havingValue = "true")
    static class DataSourceGameSlaveConfig {

        @Bean("gameSlave")
        @ConfigurationProperties(prefix = "spring.datasource.game-slave")
        public HikariDataSource gameSlave() {
            return (HikariDataSource) DataSourceBuilder.create().build();
        }
    }

    @Configuration
    @ConditionalOnProperty(prefix = "spring.datasource.clickhouse", name = "enabled", havingValue = "true")
    static class DataSourceClickHouseConfig {

        @Bean("clickhouse")
        @ConfigurationProperties(prefix = "spring.datasource.clickhouse")
        public HikariDataSource clickhouse() {
            return (HikariDataSource) DataSourceBuilder.create().build();
        }
    }

    @Configuration
    static class RedisDatabaseConfig {

        @Primary
        @Bean("redisProperties0")
        @ConfigurationProperties(prefix = "spring.redis.db0")
        public RedisProperties redisProperties0() {
            return new RedisProperties();
        }

        @Primary
        @Bean("redisConnectionFactory0")
        LettuceConnectionFactory redisConnectionFactory0(ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers, ClientResources clientResources, @Qualifier("redisProperties0") RedisProperties redisProperties) {
            LettuceClientConfiguration clientConfig = getLettuceClientConfiguration(builderCustomizers, clientResources, redisProperties);
            return createLettuceConnectionFactory(clientConfig, redisProperties);
        }

        @Primary
        @Bean("stringRedisTemplate0")
        public StringRedisTemplate stringRedisTemplate0(@Qualifier("redisConnectionFactory0") RedisConnectionFactory redisConnectionFactory) {
            StringRedisTemplate template = new StringRedisTemplate();
            template.setConnectionFactory(redisConnectionFactory);
            return template;
        }

        @Primary
        @Bean("redisTemplate")
        public RedisTemplate<String, Object> redisTemplate(@Qualifier("redisConnectionFactory0") RedisConnectionFactory redisConnectionFactory) {
            RedisTemplate<String, Object> template = new RedisTemplate<>();
            template.setConnectionFactory(redisConnectionFactory);

            // 设置key序列化方式
            template.setKeySerializer(new org.springframework.data.redis.serializer.StringRedisSerializer());
            template.setHashKeySerializer(new org.springframework.data.redis.serializer.StringRedisSerializer());

            // 设置value序列化方式
            template.setValueSerializer(new org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer());
            template.setHashValueSerializer(new org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer());

            template.afterPropertiesSet();
            return template;
        }

        @Bean("redisProperties3")
        @ConfigurationProperties(prefix = "spring.redis.db3")
        public RedisProperties redisProperties3() {
            return new RedisProperties();
        }

        @Bean("redisConnectionFactory3")
        LettuceConnectionFactory redisConnectionFactory3(ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers, ClientResources clientResources, @Qualifier("redisProperties3") RedisProperties redisProperties) {
            LettuceClientConfiguration clientConfig = getLettuceClientConfiguration(builderCustomizers, clientResources, redisProperties);
            return createLettuceConnectionFactory(clientConfig, redisProperties);
        }

        @Bean("stringRedisTemplate3")
        public StringRedisTemplate stringRedisTemplate3(@Qualifier("redisConnectionFactory3") RedisConnectionFactory redisConnectionFactory) {
            StringRedisTemplate template = new StringRedisTemplate();
            template.setConnectionFactory(redisConnectionFactory);
            return template;
        }

        @Bean("redisProperties8")
        @ConfigurationProperties(prefix = "spring.redis.db8")
        public RedisProperties redisProperties8() {
            return new RedisProperties();
        }

        @Bean("redisConnectionFactory8")
        LettuceConnectionFactory redisConnectionFactory8(ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers, ClientResources clientResources, @Qualifier("redisProperties8") RedisProperties redisProperties) {
            LettuceClientConfiguration clientConfig = getLettuceClientConfiguration(builderCustomizers, clientResources, redisProperties);
            return createLettuceConnectionFactory(clientConfig, redisProperties);
        }

        @Bean("stringRedisTemplate8")
        public StringRedisTemplate stringRedisTemplate8(@Qualifier("redisConnectionFactory8") RedisConnectionFactory redisConnectionFactory) {
            StringRedisTemplate template = new StringRedisTemplate();
            template.setConnectionFactory(redisConnectionFactory);
            return template;
        }

        @Bean("redisProperties14")
        @ConfigurationProperties(prefix = "spring.redis.db14")
        public RedisProperties redisProperties14() {
            return new RedisProperties();
        }

        @Bean("redisConnectionFactory14")
        LettuceConnectionFactory redisConnectionFactory14(ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers, ClientResources clientResources, @Qualifier("redisProperties14") RedisProperties redisProperties) {
            LettuceClientConfiguration clientConfig = getLettuceClientConfiguration(builderCustomizers, clientResources, redisProperties);
            return createLettuceConnectionFactory(clientConfig, redisProperties);
        }

        @Bean("stringRedisTemplate14")
        public StringRedisTemplate stringRedisTemplate14(@Qualifier("redisConnectionFactory14") RedisConnectionFactory redisConnectionFactory) {
            StringRedisTemplate template = new StringRedisTemplate();
            template.setConnectionFactory(redisConnectionFactory);
            return template;
        }

        private LettuceConnectionFactory createLettuceConnectionFactory(LettuceClientConfiguration clientConfiguration, RedisProperties redisProperties) {
            RedisSentinelConfiguration sentinelConfig = getSentinelConfig(redisProperties);
            if (sentinelConfig != null) {
                return new LettuceConnectionFactory(sentinelConfig, clientConfiguration);
            }
            RedisClusterConfiguration clusterConfiguration = getClusterConfiguration(redisProperties);
            if (clusterConfiguration != null) {
                return new LettuceConnectionFactory(clusterConfiguration, clientConfiguration);
            }
            return new LettuceConnectionFactory(getStandaloneConfig(redisProperties), clientConfiguration);
        }

        private RedisSentinelConfiguration getSentinelConfig(RedisProperties redisProperties) {
            RedisProperties.Sentinel sentinelProperties = redisProperties.getSentinel();
            if (sentinelProperties != null) {
                RedisSentinelConfiguration config = new RedisSentinelConfiguration();
                config.master(sentinelProperties.getMaster());
                config.setSentinels(createSentinels(sentinelProperties));
                if (redisProperties.getPassword() != null) {
                    config.setPassword(RedisPassword.of(redisProperties.getPassword()));
                }
                if (sentinelProperties.getPassword() != null) {
                    config.setSentinelPassword(RedisPassword.of(sentinelProperties.getPassword()));
                }
                config.setDatabase(redisProperties.getDatabase());
                return config;
            }
            return null;
        }

        private RedisClusterConfiguration getClusterConfiguration(RedisProperties redisProperties) {
            if (redisProperties.getCluster() == null) {
                return null;
            }
            RedisProperties.Cluster clusterProperties = redisProperties.getCluster();
            RedisClusterConfiguration config = new RedisClusterConfiguration(clusterProperties.getNodes());
            if (clusterProperties.getMaxRedirects() != null) {
                config.setMaxRedirects(clusterProperties.getMaxRedirects());
            }
            if (redisProperties.getPassword() != null) {
                config.setPassword(RedisPassword.of(redisProperties.getPassword()));
            }
            return config;
        }

        private RedisStandaloneConfiguration getStandaloneConfig(RedisProperties redisProperties) {
            RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
            config.setHostName(redisProperties.getHost());
            config.setPort(redisProperties.getPort());
            config.setPassword(RedisPassword.of(redisProperties.getPassword()));
            config.setDatabase(redisProperties.getDatabase());
            return config;
        }

        private List<RedisNode> createSentinels(RedisProperties.Sentinel sentinel) {
            List<RedisNode> nodes = new ArrayList<>();
            for (String node : sentinel.getNodes()) {
                try {
                    String[] parts = StringUtils.split(node, ":");
                    Assert.state(parts.length == 2, "Must be defined as 'host:port'");
                    nodes.add(new RedisNode(parts[0], Integer.parseInt(parts[1])));
                } catch (RuntimeException ex) {
                    throw new IllegalStateException("Invalid redis sentinel property '" + node + "'", ex);
                }
            }
            return nodes;
        }

        private LettuceClientConfiguration getLettuceClientConfiguration(ObjectProvider<LettuceClientConfigurationBuilderCustomizer> builderCustomizers, ClientResources clientResources, RedisProperties redisProperties) {
            LettuceClientConfiguration.LettuceClientConfigurationBuilder builder = createBuilder(redisProperties.getLettuce().getPool());
            applyProperties(builder, redisProperties);
            builder.clientOptions(initializeClientOptionsBuilder(redisProperties).timeoutOptions(TimeoutOptions.enabled()).build());
            builder.clientResources(clientResources);
            builderCustomizers.orderedStream().forEach((customizer) -> customizer.customize(builder));
            return builder.build();
        }

        private ClientOptions.Builder initializeClientOptionsBuilder(RedisProperties redisProperties) {
            if (redisProperties.getCluster() != null) {
                ClusterClientOptions.Builder builder = ClusterClientOptions.builder();
                RedisProperties.Lettuce.Cluster.Refresh refreshProperties = redisProperties.getLettuce().getCluster().getRefresh();
                ClusterTopologyRefreshOptions.Builder refreshBuilder = ClusterTopologyRefreshOptions.builder();
                if (refreshProperties.getPeriod() != null) {
                    refreshBuilder.enablePeriodicRefresh(refreshProperties.getPeriod());
                }
                if (refreshProperties.isAdaptive()) {
                    refreshBuilder.enableAllAdaptiveRefreshTriggers();
                }
                return builder.topologyRefreshOptions(refreshBuilder.build());
            }
            return ClientOptions.builder();
        }

        private void applyProperties(LettuceClientConfiguration.LettuceClientConfigurationBuilder builder, RedisProperties redisProperties) {
            if (redisProperties.isSsl()) {
                builder.useSsl();
            }
            if (redisProperties.getTimeout() != null) {
                builder.commandTimeout(redisProperties.getTimeout());
            }
            if (redisProperties.getLettuce() != null) {
                RedisProperties.Lettuce lettuce = redisProperties.getLettuce();
                if (lettuce.getShutdownTimeout() != null && !lettuce.getShutdownTimeout().isZero()) {
                    builder.shutdownTimeout(redisProperties.getLettuce().getShutdownTimeout());
                }
            }
            if (StringUtils.hasText(redisProperties.getClientName())) {
                builder.clientName(redisProperties.getClientName());
            }
        }

        private LettuceClientConfiguration.LettuceClientConfigurationBuilder createBuilder(RedisProperties.Pool pool) {
            if (pool == null) {
                return LettuceClientConfiguration.builder();
            }
            return LettucePoolingClientConfiguration.builder().poolConfig(getPoolConfig(pool));
        }

        private GenericObjectPoolConfig<?> getPoolConfig(RedisProperties.Pool properties) {
            GenericObjectPoolConfig<?> config = new GenericObjectPoolConfig<>();
            config.setMaxTotal(properties.getMaxActive());
            config.setMaxIdle(properties.getMaxIdle());
            config.setMinIdle(properties.getMinIdle());
            if (properties.getTimeBetweenEvictionRuns() != null) {
                config.setTimeBetweenEvictionRunsMillis(properties.getTimeBetweenEvictionRuns().toMillis());
            }
            if (properties.getMaxWait() != null) {
                config.setMaxWaitMillis(properties.getMaxWait().toMillis());
            }
            return config;
        }
    }
}
