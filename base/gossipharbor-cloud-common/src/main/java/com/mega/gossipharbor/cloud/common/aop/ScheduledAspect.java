package com.mega.gossipharbor.cloud.common.aop;

import com.google.common.base.Splitter;
import com.mega.gossipharbor.cloud.common.CommonConfig;
import com.mega.gossipharbor.cloud.common.utils.RedisUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Aspect
@Slf4j
public class ScheduledAspect {
    private final RedisUtils redisUtils;
    private final List<IgnoreScheduleTask> ignoreScheduleTasks;

    public ScheduledAspect(RedisUtils redisUtils) {
        this.redisUtils = redisUtils;
        ignoreScheduleTasks = new ArrayList<>();
    }


    /**
     * 该AOP的作用
     * 1、是禁止beta环境执行一切定时任务
     * 2、如果启动多节点，添加redis锁保证只有一个程序可以执行任务
     * 3、作为规范，禁止在非*schedule*包下写定时任务
     */
    @Around("@annotation(scheduled)")
    public Object around(ProceedingJoinPoint joinPoint, Scheduled scheduled) throws Throwable {
        Object result = null;
        if (CommonConfig.ENV.equals("beta") || CommonConfig.ENV.equals("hk") || CommonConfig.ENV.equals("testhk")) {
            log.info("灰度环境不执行定时任务");
            return null;
        }
        String methodSignature = joinPoint.getSignature().toShortString();
        List<String> layers = Splitter.on(".").trimResults().splitToList(joinPoint.getSignature().getDeclaringTypeName());
        if (!layers.get(layers.size()-2).toLowerCase().contains("schedule")) {
            throw new RuntimeException("禁止在非*schedule*包下写定时任务，请修改代码，错误方法名: " + methodSignature);
        }
        Date startDate = new Date();
        for (IgnoreScheduleTask ignoreScheduleTask: ignoreScheduleTasks) {
            if (methodSignature.equals(ignoreScheduleTask.getMethodSignature()) && CommonConfig.ENV.equals(ignoreScheduleTask.getEnv())) {
                // 特殊屏蔽
                log.info("定时任务: {} 拒绝执行，ScheduledAspect特殊屏蔽", methodSignature);
                return null;
            }
        }
        if (redisUtils.tryLock(methodSignature + "-" + CommonConfig.ENV, 5, TimeUnit.SECONDS)) {
            log.info("定时任务: {} 开始执行", methodSignature);
            Date endDate = new Date();
            result = joinPoint.proceed();
            redisUtils.releaseLock(methodSignature + "-" + CommonConfig.ENV);
            log.info("定时任务: {} 执行完成，时间{}ms", methodSignature, endDate.getTime() - startDate.getTime());
        } else {
            log.info("定时任务: {} 正在被其他服务执行，本次不执行", methodSignature);
        }
        return result;
    }

    @Data
    @Accessors(chain = true)
    public static class IgnoreScheduleTask {
        private String env;
        private String methodSignature;
    }
}
