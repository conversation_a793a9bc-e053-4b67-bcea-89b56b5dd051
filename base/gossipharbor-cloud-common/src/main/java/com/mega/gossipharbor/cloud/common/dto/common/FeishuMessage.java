package com.mega.gossipharbor.cloud.common.dto.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class FeishuMessage {
    @JsonProperty("msg_type")
    private String msgType = "interactive";
    private Card card;

    @Data
    public static class Card {
        private List<Elements> elements;
        private Header header;

        @Data
        public static class Elements {
            private String tag = "div";
            private Text text;

            @Data
            public static class Text {
                private String content;
                private String tag = "lark_md";
            }
        }

        @Data
        public static class Header {
            private Title title;

            @Data
            public static class Title {
                private String content;
                private String tag = "plain_text";
            }
        }
    }
}
