package com.mega.gossipharbor.cloud.common.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.mega.gossipharbor.cloud.common.vo.ExchangeTransactionReqVO;
import com.mega.gossipharbor.cloud.core.Result;

import io.swagger.annotations.ApiOperation;

@FeignClient(value = "gossipharbor-cloud-exchange-" + "${game.region-id}-"+ "${spring.profiles.active}", contextId = "gossipharbor-cloud-exchange-client")
public interface GossipharborExchangeClient {
    @ApiOperation("物品增加活减少")
    @PostMapping("/exchange/api/transaction")
    Result<?> exchangeTransaction(@Validated @RequestBody ExchangeTransactionReqVO vo) throws Exception;
    
}