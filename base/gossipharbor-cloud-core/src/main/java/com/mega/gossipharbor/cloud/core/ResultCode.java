package com.mega.gossipharbor.cloud.core;

import com.mega.gossipharbor.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum ResultCode {

    SUCCESS(1),
    ERROR(0),
    NOT_AUTHENTICATE_ERROR(2000),
    NOT_AUTHORIZE_ERROR(3000),
    PARAM_ERROR(4000);

    private final Integer code;

    ResultCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
