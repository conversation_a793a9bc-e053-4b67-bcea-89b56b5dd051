package com.mega.gossipharbor.cloud.core.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 日期工具类
 */
public class DateUtils {

    public static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    public static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private static final DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Date转换成 yyyy-MM-dd 00:00:00
     */
    public static Date truncateDate(Date date) {
        return org.apache.commons.lang3.time.DateUtils.truncate(date, Calendar.DAY_OF_MONTH);
    }

    /**
     * Date转换成LocalDateTime
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).toLocalDateTime();
    }

    public static Date addDate(Date date, Integer amount) {
        return org.apache.commons.lang3.time.DateUtils.addDays(date, amount);
    }
    public static Date addHour(Date date, Integer amount) {
        return org.apache.commons.lang3.time.DateUtils.addHours(date, amount);
    }

    public static Date addSeconds(Date date, Integer amount) {
        return org.apache.commons.lang3.time.DateUtils.addSeconds(date, amount);
    }


    /**
     * 判断一个时间是否在另一个时间之前
     *
     * @param time1 第一个时间
     * @param time2 第二个时间
     * @return 判断结果
     */
    public static boolean before(String time1, String time2) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date dateTime1 = format.parse(time1);
        Date dateTime2 = format.parse(time2);
        return dateTime1.before(dateTime2);
    }

    /**
     * 判断一个时间是否在另一个时间之后
     *
     * @param time1 第一个时间
     * @param time2 第二个时间
     * @return 判断结果
     */
    public static boolean after(String time1, String time2) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date dateTime1 = format.parse(time1);
        Date dateTime2 = format.parse(time2);
        return dateTime1.after(dateTime2);
    }


    /**
     * 判断一个时间是否在区间内
     *
     * @param date 要判断的时间
     * @param startStr 开始时间
     * @param endStr 结束时间
     * @return 判断结果
     */
    public static boolean between(Date date, String startStr, String endStr) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = format.parse(startStr);
        Date end = format.parse(endStr);
        return date.after(start) && date.before(end);
    }

    /**
     * 计算时间差值（单位为秒）
     *
     * @param time1 时间1
     * @param time2 时间2
     * @return 差值
     */
    public static long minus(String time1, String time2) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date datetime1 = format.parse(time1);
        Date datetime2 = format.parse(time2);
        long millisecond = datetime1.getTime() - datetime2.getTime();
        return millisecond / 1000;
    }

    /**
     * 获取年月日和小时
     *
     * @param datetime 时间（yyyy-MM-dd HH:mm:ss）
     * @return 结果
     */
    public static String getDateHour(String datetime) {
        String date = datetime.split(" ")[0];
        String hourMinuteSecond = datetime.split(" ")[1];
        String hour = hourMinuteSecond.split(":")[0];
        return date + "_" + hour;
    }

    public static String getDateStr(SimpleDateFormat dateFormat, Date date) {
        return dateFormat.format(date);
    }

    /**
     * 获取当天日期（yyyy-MM-dd）
     *
     * @return 当天日期
     */
    public static String getTodayDate() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(new Date());
    }

    /**
     * 获取昨天的日期（yyyy-MM-dd）
     *
     * @return 昨天的日期
     */
    public static String getYesterdayDate() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_YEAR, -1);
        Date date = cal.getTime();
        return format.format(date);
    }

    /**
     * 格式化日期（yyyy-MM-dd）
     *
     * @param date Date对象
     * @return 格式化后的日期
     */
    public static String formatDate(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(date);
    }

    /**
     * 格式化时间（yyyy-MM-dd HH:mm:ss）
     *
     * @param date Date对象
     * @return 格式化后的时间
     */
    public static String formatTime(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return format.format(date);
    }

    /**
     * 格式化时间（HH:mm）
     *
     * @param date Date对象
     * @return 格式化后的时间
     */
    public static String formatHourMinute(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        return format.format(date);
    }

    /**
     * 获得当天零时零分零秒
     */
    public static Date getToday() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 通过日期获取当前周的起始时间
     */
    public static Date getWeekStart(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 获得当前日期是一个星期的第几天
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        // 获得当前日期是一个星期的第几天
        int day = cal.get(Calendar.DAY_OF_WEEK);
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        return cal.getTime();
    }

    /**
     * 通过日期获取当前周的周日时间
     */
    public static Date getWeekEnd(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 设置一个星期的第七天，按中国的习惯一个星期的第七天是星期日
        cal.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        // 获得当前日期是一个星期的第几天
        int day = cal.get(Calendar.DAY_OF_WEEK);
        cal.add(Calendar.DATE, 7 - cal.getFirstDayOfWeek() + day);
        return cal.getTime();
    }

    public static Date getWeekEndDate(Date date) {
        Date weekEnd = getWeekEnd(date);
        return truncateDate(weekEnd);
    }

    /**
     * 通过年份和周获取日期
     *
     * @param year 年份
     * @param week 第几周
     */
    public static Date weekToDayFormat(int year, int week) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        // ①.设置该年份的开始日期：第一个月的第一天
        calendar.set(year, Calendar.JANUARY, 1);
        // ②.计算出第一周还剩几天：+1是因为1号是1天
        int dayOfWeek = 7 - calendar.get(Calendar.DAY_OF_WEEK) + 1;
        // ③.周数减去第一周再减去要得到的周
        week = week - 2;
        // ④.计算起止日期
        calendar.add(Calendar.DAY_OF_YEAR, week * 7 + dayOfWeek + 1);
        return format.parse(formatDate(calendar.getTime()));
    }

    public static Long getCurrentTimestamp() {
        return new Date().getTime() / 1000;
    }

    public static String fixTimeZoneFormat(String time) {
        // 匹配时区部分，如+8:00或-8:00等
        Pattern pattern = Pattern.compile("(\\+|-)(\\d{1})(:\\d{2})$");
        Matcher matcher = pattern.matcher(time);

        // 如果匹配成功，添加前导零
        if (matcher.find()) {
            time = matcher.replaceFirst(matcher.group(1) + "0" + matcher.group(2) + matcher.group(3));
        }

        return time;
    }

    public static ZonedDateTime string2ZonedDateTime(String subscriptionStartTime){
        // 正则表达式匹配时区部分
        String correctedTime = fixTimeZoneFormat(subscriptionStartTime);

        System.out.println(correctedTime);

        // 使用自定义格式解析时间
        OffsetDateTime offsetDateTime = OffsetDateTime.parse(correctedTime);

        // 转换为 ZonedDateTime
        ZonedDateTime zonedDateTime = offsetDateTime.toZonedDateTime();
        return zonedDateTime;
    }

    /**
     * Date时间加减
     */
    public static Date adjustDateByDays(Date date, Integer days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }

    /**
     * 计算时间差值（单位为天）
     *
     * @param currentPeriodStart 时间1
     * @param currentPeriodEnd 时间2
     * @return 差值
     */
    public static Integer days(Long currentPeriodStart, Long currentPeriodEnd){
        long diffInSeconds = currentPeriodEnd - currentPeriodStart;
        // 将秒转换为天数
        Integer diffInDays = Math.toIntExact(diffInSeconds / (24 * 60 * 60)); // 1天 = 24小时 * 60分钟 * 60秒
        return diffInDays;
    }

    /**
     * LocalDateTime转Date
     */
    public static Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
    
    /**
     * Date转LocalDateTime
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }
    
    /**
     * 格式化LocalDateTime为字符串
     */
    public static String format(LocalDateTime localDateTime) {
        return localDateTime.format(DEFAULT_FORMATTER);
    }
    
    /**
     * 字符串转LocalDateTime
     */
    public static LocalDateTime parse(String dateStr) {
        return LocalDateTime.parse(dateStr, DEFAULT_FORMATTER);
    }
    
    /**
     * 获取当天开始时间
     */
    public static LocalDateTime getTodayStart() {
        return LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0);
    }
    
    /**
     * 获取当天结束时间
     */
    public static LocalDateTime getTodayEnd() {
        return LocalDateTime.now().withHour(23).withMinute(59).withSecond(59).withNano(999999999);
    }
    
    /**
     * 判断日期是否为今天
     */
    public static boolean isToday(LocalDateTime dateTime) {
        LocalDateTime now = LocalDateTime.now();
        return dateTime.getYear() == now.getYear() 
            && dateTime.getMonthValue() == now.getMonthValue() 
            && dateTime.getDayOfMonth() == now.getDayOfMonth();
    }
}
