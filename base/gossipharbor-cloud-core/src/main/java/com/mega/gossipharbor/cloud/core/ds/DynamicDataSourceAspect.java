package com.mega.gossipharbor.cloud.core.ds;


import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Aspect
@Order(-1)
public class DynamicDataSourceAspect {

    @Before("@annotation(ds)")
    public void before(JoinP<PERSON> joinPoint, DS ds) {
        System.out.println("ds name: " + joinPoint.getSignature().getName() + "  " + ds.value());
        DynamicDataSourceContext.set(ds.value());
    }

    @After("@annotation(ds)")
    public void after(JoinPoint joinPoint, DS ds) {
        DynamicDataSourceContext.remove();
    }
}
