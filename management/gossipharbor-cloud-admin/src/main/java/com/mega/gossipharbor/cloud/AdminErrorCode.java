package com.mega.gossipharbor.cloud;

import com.mega.gossipharbor.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum AdminErrorCode {

    ERR_0(0),
    ;

    private final Integer code;

    AdminErrorCode(Integer code) {
        this.code = code;
    }

    public static AdminErrorCode getExchangeCode(Integer code) {
        for (AdminErrorCode exchangeCode : AdminErrorCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
