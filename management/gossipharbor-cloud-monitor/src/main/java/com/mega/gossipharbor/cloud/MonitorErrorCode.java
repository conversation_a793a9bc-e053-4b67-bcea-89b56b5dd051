package com.mega.gossipharbor.cloud;

import com.mega.gossipharbor.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum MonitorErrorCode {

    ERR_0(0),
    ;

    private final Integer code;

    MonitorErrorCode(Integer code) {
        this.code = code;
    }

    public static MonitorErrorCode getExchangeCode(Integer code) {
        for (MonitorErrorCode exchangeCode : MonitorErrorCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
