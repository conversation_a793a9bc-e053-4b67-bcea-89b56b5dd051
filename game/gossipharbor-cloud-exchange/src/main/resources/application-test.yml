logging:
  file:
    path: /opt/log/${spring.cloud.client.hostname}/
  logback:
    rollingpolicy:
      max-file-size: 128MB
spring:
  data:
    mongodb:
      uri: mongodb://werewolf-cloud:Mangosteen0!@*************:27017,*************:27018/werewolf?maxPoolSize=500&minPoolSize=10
  cloud:
    consul:
      enabled: true
      host: *************
      port: 9910
      discovery:
        prefer-ip-address: true
        ip-address: *************
        instance-id: ${spring.application.name}-${spring.cloud.consul.discovery.ip-address}-${server.port}
        service-name: ${spring.application.name}
        health-check-critical-timeout: 1m
  datasource:
    master:
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 4
    slave:
      enabled: true
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 4
    game-master:
      enabled: true
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: *********************************************************-${game.region-id}?useUnicode=true&characterEncoding=UTF-8&useSSL=false&autoReconnect=true&failOverReadOnly=false&cachePrepStmts=true&prepStmtCacheSize=256&prepStmtCacheSqlLimit=2048&useServerPrepStmts=true&useLocalSessionState=true&rewriteBatchedStatements=true&cacheResultSetMetadata=true&cacheServerConfiguration=true&elideSetAutoCommits=true&maintainTimeStats=false&zeroDateTimeBehavior=convertToNull
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 4
    game-slave:
      enabled: true
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: *********************************************************-${game.region-id}?useUnicode=true&characterEncoding=UTF-8&useSSL=false&autoReconnect=true&failOverReadOnly=false&cachePrepStmts=true&prepStmtCacheSize=256&prepStmtCacheSqlLimit=2048&useServerPrepStmts=true&useLocalSessionState=true&rewriteBatchedStatements=true&cacheResultSetMetadata=true&cacheServerConfiguration=true&elideSetAutoCommits=true&maintainTimeStats=false&zeroDateTimeBehavior=convertToNull
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 4
  redis:
    db0:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26380
          - *************:26381
      database: 0
      password: LA1954b!
    db3:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26380
          - *************:26381
      database: 3
      password: LA1954b!
    db8:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26380
          - *************:26381
      database: 8
      password: LA1954b!
    db14:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26380
          - *************:26381
      database: 14
      password: LA1954b!
  kafka:
    bootstrap-servers:
      - 172.25.220.245:9992
    producer:
      acks: 0
      retries: 0
      properties:
        linger.ms: 1000