<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.gossipharbor.cloud.exchange.secure.dao.TransactionDao">

    <!-- 根据道具ID查询道具类型编码 -->
    <select id="selectItemCateCodeByItemId" resultType="java.lang.String">
        SELECT ic.code
        FROM item i
        INNER JOIN item_cate ic ON i.item_cate_id = ic.id
        WHERE i.id = #{itemId}
        AND i.delsign = 0
    </select>

    <!-- 查询角色货币库存 -->
    <select id="selectCharacterCurrencyAmount" resultType="java.lang.Long">
        SELECT COALESCE(current_amount, 0)
        FROM character_item_currency
        WHERE character_id = #{characterId}
        AND item_id = #{itemId}
    </select>

    <!-- 查询角色消耗品库存 -->
    <select id="selectCharacterConsumableAmount" resultType="java.lang.Long">
        SELECT COALESCE(current_amount, 0)
        FROM character_consumable
        WHERE character_id = #{characterId}
        AND item_id = #{itemId}
    </select>

    <!-- 查询角色材料库存 -->
    <select id="selectCharacterMaterialAmount" resultType="java.lang.Long">
        SELECT COALESCE(current_amount, 0)
        FROM character_material
        WHERE character_id = #{characterId}
        AND item_id = #{itemId}
    </select>

    <!-- 更新或插入角色货币库存 -->
    <insert id="upsertCharacterCurrency">
        INSERT INTO character_item_currency (character_id, item_id, current_amount)
        VALUES (#{characterId}, #{itemId}, #{changeAmount})
        ON DUPLICATE KEY UPDATE
        current_amount = current_amount + #{changeAmount}
    </insert>

    <!-- 更新或插入角色消耗品库存 -->
    <insert id="upsertCharacterConsumable">
        INSERT INTO character_consumable (character_id, item_id, current_amount)
        VALUES (#{characterId}, #{itemId}, #{changeAmount}) 
        ON DUPLICATE KEY UPDATE
        current_amount = current_amount + #{changeAmount}
    </insert>

    <!-- 更新或插入角色材料库存 -->
    <insert id="upsertCharacterMaterial">
        INSERT INTO character_material (character_id, item_id, current_amount)
        VALUES (#{characterId}, #{itemId}, #{changeAmount})
        ON DUPLICATE KEY UPDATE
        current_amount = current_amount + #{changeAmount}
    </insert>



    <!-- 检查表是否存在 -->
    <select id="checkTableExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = #{tableName}
    </select>

    <!-- 创建item_turnover表 -->
    <update id="createItemTurnoverTable">
        CREATE TABLE IF NOT EXISTS ${tableName} (
            `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
            `character_id` BIGINT NOT NULL COMMENT '用户ID', 
            `item_id` BIGINT NOT NULL COMMENT '道具ID',
            `change_amount` BIGINT NOT NULL COMMENT '变更数量(正数为增加,负数为减少)',
            `transaction_id` VARCHAR(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '事务id,一般为生成的uuid',
            `business_dic_id` VARCHAR(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务字典id',
            `turnover_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '流水时间',
            `turnover_date` DATE NOT NULL COMMENT '流水日期',
            `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            `delsign` TINYINT(1) NOT NULL DEFAULT '0',
            PRIMARY KEY (`id`),
            KEY `idx_character_id` (`character_id`),
            KEY `idx_item_id` (`item_id`),
            KEY `idx_create_time` (`turnover_time`),
            KEY `idx_create_date` (`turnover_date`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='道具流水表'
    </update>

    <!-- 批量插入道具流水记录 -->
    <insert id="batchInsertItemTurnover">
        INSERT INTO ${tableName} (
            character_id, 
            item_id, 
            change_amount, 
            transaction_id,
            business_dic_id,
            turnover_time,
            turnover_date
        )
        VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.characterId}, 
                #{record.itemId}, 
                #{record.changeAmount}, 
                #{record.transactionId},
                #{record.businessDicId}, 
                #{record.turnoverTime}, 
                #{record.turnoverDate}
            )
        </foreach>
    </insert>

</mapper>