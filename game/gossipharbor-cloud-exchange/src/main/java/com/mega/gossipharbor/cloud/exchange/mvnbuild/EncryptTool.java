package com.mega.gossipharbor.cloud.exchange.mvnbuild;


import com.mega.gossipharbor.cloud.exchange.security.EncryptedService;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Stream;

/**
 * 文件加密工具
 * 用于在Maven构建过程中批量加密指定的文件（.class、.xml等）
 *
 * 使用方法:
 * java com.mega.gossipharbor.cloud.exchange.mvnbuild.EncryptTool <input-path> <output-path>
 *
 * 支持:
 * - 目录加密：递归加密目录中的所有文件
 * - 单文件加密：加密指定的单个文件
 *
 * 密钥获取优先级:
 * 1. 系统属性: -Dencrypt.key=xxx
 * 2. 环境变量: ENCRYPT_KEY=xxx
 * 3. 默认密钥 (仅开发环境)
 */
public class EncryptTool {
    
    public static void main(String[] args) {
        try {
            if (args.length != 2) {
                printUsage();
                System.exit(1);
            }

            Path inputPath = Paths.get(args[0]);
            Path outputPath = Paths.get(args[1]);

            // 创建EncryptedService实例来使用其加密方法
            EncryptedService encryptedService = new EncryptedService();

            System.out.println("Starting encryption process...");
            System.out.println("Input path: " + inputPath.toAbsolutePath());
            System.out.println("Output path: " + outputPath.toAbsolutePath());

            int encryptedCount;

            if (Files.isDirectory(inputPath)) {
                // 目录加密
                validateInputs(inputPath, outputPath);
                Files.createDirectories(outputPath);
                encryptedCount = encryptDirectory(inputPath, outputPath, encryptedService);
            } else if (Files.isRegularFile(inputPath)) {
                // 单文件加密
                Files.createDirectories(outputPath.getParent());
                encryptSingleFile(inputPath, outputPath, encryptedService);
                encryptedCount = 1;
            } else {
                throw new IllegalArgumentException("Input path must be a file or directory: " + inputPath);
            }

            System.out.println("Encryption completed successfully!");
            System.out.println("Total files encrypted: " + encryptedCount);

        } catch (Exception e) {
            System.err.println("Encryption failed: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
    
    /**
     * 打印使用说明
     */
    private static void printUsage() {
        System.err.println("Usage: java com.example.build.EncryptTool <input-path> <output-path>");
        System.err.println("");
        System.err.println("Arguments:");
        System.err.println("  input-path   File or directory to encrypt");
        System.err.println("  output-path  Output file or directory for encrypted content");
        System.err.println("");
        System.err.println("Key configuration (priority order):");
        System.err.println("  1. System property: -Dencrypt.key=<key>");
        System.err.println("  2. Environment variable: ENCRYPT_KEY=<key>");
        System.err.println("  3. Default key (development only)");
        System.err.println("");
        System.err.println("Examples:");
        System.err.println("  # Encrypt directory");
        System.err.println("  java -Dencrypt.key=mykey123 com.example.build.EncryptTool target/classes/secure target/encrypted");
        System.err.println("  # Encrypt single file");
        System.err.println("  java -Dencrypt.key=mykey123 com.example.build.EncryptTool src/main/resources/mapper/Test.xml target/encrypted/Test.xml");
    }
    
    /**
     * 验证输入参数
     */
    private static void validateInputs(Path inputDir, Path outputDir) throws Exception {
        if (!Files.exists(inputDir)) {
            throw new IllegalArgumentException("Input directory does not exist: " + inputDir);
        }
        
        if (!Files.isDirectory(inputDir)) {
            throw new IllegalArgumentException("Input path is not a directory: " + inputDir);
        }
        
        if (inputDir.equals(outputDir)) {
            throw new IllegalArgumentException("Input and output directories cannot be the same");
        }
    }
    

    
    /**
     * 加密单个文件
     */
    private static void encryptSingleFile(Path inputFile, Path outputFile, EncryptedService encryptedService) throws Exception {
        System.out.println("Encrypting file: " + inputFile.getFileName());

        // 读取文件内容
        byte[] fileData = Files.readAllBytes(inputFile);

        // 使用EncryptedService实例方法进行加密
        byte[] encryptedData = encryptedService.encrypt(fileData);

        // 写入加密文件
        Files.write(outputFile, encryptedData);

        System.out.println("Encrypted: " + outputFile.toString().replace(System.getProperty("user.dir") + File.separator, ""));
    }

    /**
     * 递归加密目录中的所有文件
     */
    private static int encryptDirectory(Path inputDir, Path outputDir, EncryptedService encryptedService) throws Exception {
        int count = 0;

        try (Stream<Path> paths = Files.walk(inputDir)) {
            for (Path path : paths.filter(Files::isRegularFile)
                                  .toArray(Path[]::new)) {
                encryptFile(path, inputDir, outputDir, encryptedService);
                count++;
            }
        }

        return count;
    }
    
    /**
     * 加密单个文件
     */
    private static void encryptFile(Path inputFile, Path inputDir, Path outputDir, EncryptedService encryptedService)
            throws Exception {
        try {
            // 读取原始文件
            byte[] plainBytes = Files.readAllBytes(inputFile);

            // 使用EncryptedService实例方法进行AES加密
            byte[] encryptedBytes = encryptedService.encrypt(plainBytes);

            // 计算相对路径并创建输出文件
            Path relativePath = inputDir.relativize(inputFile);
            Path outputFile = outputDir.resolve(relativePath);

            // 确保输出目录存在
            Files.createDirectories(outputFile.getParent());

            // 写入加密文件
            Files.write(outputFile, encryptedBytes);

            System.out.println("Encrypted: " + relativePath);

        } catch (Exception e) {
            throw new Exception("Failed to encrypt file: " + inputFile, e);
        }
    }
    
    /**
     * 验证加密解密功能（使用EncryptedService实例方法）
     */
    public static boolean verifyEncryption(byte[] originalData, EncryptedService encryptedService) {
        try {
            // 使用实例方法进行加密和解密验证
            byte[] encrypted = encryptedService.encrypt(originalData);
            byte[] decrypted = encryptedService.decrypt(encrypted);

            if (originalData.length != decrypted.length) {
                return false;
            }

            for (int i = 0; i < originalData.length; i++) {
                if (originalData[i] != decrypted[i]) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
