package com.mega.gossipharbor.cloud.exchange.service;

import com.mega.gossipharbor.cloud.common.entity.Dic;
import com.mega.gossipharbor.cloud.common.mapper.DicMapper;
import com.mega.gossipharbor.cloud.core.ds.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DynamicDataSourceService {

    private final DicMapper dicMapper;

    @Autowired
    public DynamicDataSourceService(DicMapper dicMapper) {
        this.dicMapper = dicMapper;
    }

    @DS("master")
    public void getDicMaster() {
        Dic dic = dicMapper.selectByPrimaryKey(1L);
        System.out.println(dic.getValue());
    }

    @DS("gameMaster")
    public void getDicGameMaster() {
        Dic dic = dicMapper.selectByPrimaryKey(1L);
        System.out.println(dic.getValue());
    }
}