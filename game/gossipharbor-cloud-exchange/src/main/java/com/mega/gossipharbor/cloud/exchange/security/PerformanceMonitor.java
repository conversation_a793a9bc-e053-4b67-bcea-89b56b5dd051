package com.mega.gossipharbor.cloud.exchange.security;

import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import lombok.extern.slf4j.Slf4j;
/**
 * 性能监控组件
 * 
 * 监控加密类加载的性能指标
 */
@Component
@Slf4j
public class PerformanceMonitor {
    
    // 类加载统计
    private final AtomicLong totalClassLoads = new AtomicLong(0);
    private final AtomicLong encryptedClassLoads = new AtomicLong(0);
    private final AtomicLong failedClassLoads = new AtomicLong(0);
    
    // 时间统计
    private final AtomicLong totalDecryptionTime = new AtomicLong(0);
    private final AtomicLong maxDecryptionTime = new AtomicLong(0);
    private final AtomicLong minDecryptionTime = new AtomicLong(Long.MAX_VALUE);
    
    // 缓存统计
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    
    // 详细统计
    private final ConcurrentHashMap<String, ClassLoadStats> classStats = new ConcurrentHashMap<>();
    private final AtomicReference<String> slowestClass = new AtomicReference<>("");
    
    /**
     * 记录类加载开始
     */
    public long recordClassLoadStart(String className) {
        totalClassLoads.incrementAndGet();
        return System.currentTimeMillis();
    }
    
    /**
     * 记录加密类加载完成
     */
    public void recordEncryptedClassLoadComplete(String className, long startTime, boolean fromCache) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        encryptedClassLoads.incrementAndGet();
        totalDecryptionTime.addAndGet(duration);
        
        // 更新最大最小时间
        updateMaxTime(duration);
        updateMinTime(duration);
        
        // 记录缓存统计
        if (fromCache) {
            cacheHits.incrementAndGet();
        } else {
            cacheMisses.incrementAndGet();
        }
        
        // 更新类统计
        updateClassStats(className, duration);
        
        // 检查是否是最慢的类
        if (duration > maxDecryptionTime.get()) {
            slowestClass.set(className);
        }
        
        // 性能警告
        if (duration > 1000) { // 超过1秒
            log.warn("⚠️ [PERFORMANCE] Slow class loading detected: {} took {}ms", className, duration);
        }
    }
    
    /**
     * 记录类加载失败
     */
    public void recordClassLoadFailure(String className, Exception e) {
        failedClassLoads.incrementAndGet();

        // 只记录我们关心的类加载失败（加密类相关）
        if (className.startsWith("com.example.secure")) {
            log.error("❌ [PERFORMANCE] Encrypted class load failed: {} - {}", className, e.getMessage());
        }
        // // 其他类加载失败只在调试模式下显示
        // else if (Boolean.parseBoolean(System.getProperty("encrypt.debug", "false"))) {
        //     System.out.println("❌ [PERFORMANCE] Class load failed: " + className + " - " + e.getMessage());
        // }
    }
    
    /**
     * 获取性能统计报告
     */
    public PerformanceReport getPerformanceReport() {
        PerformanceReport report = new PerformanceReport();
        
        // 基本统计
        report.totalClassLoads = totalClassLoads.get();
        report.encryptedClassLoads = encryptedClassLoads.get();
        report.failedClassLoads = failedClassLoads.get();
        report.successRate = calculateSuccessRate();
        
        // 时间统计
        report.totalDecryptionTime = totalDecryptionTime.get();
        report.averageDecryptionTime = calculateAverageDecryptionTime();
        report.maxDecryptionTime = maxDecryptionTime.get();
        report.minDecryptionTime = minDecryptionTime.get() == Long.MAX_VALUE ? 0 : minDecryptionTime.get();
        
        // 缓存统计
        report.cacheHits = cacheHits.get();
        report.cacheMisses = cacheMisses.get();
        report.cacheHitRate = calculateCacheHitRate();
        
        // 其他信息
        report.slowestClass = slowestClass.get();
        report.classCount = classStats.size();
        
        return report;
    }
    
    /**
     * 获取详细的类统计信息
     */
    public String getDetailedStats() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== Detailed Performance Statistics ===\n");
        
        PerformanceReport report = getPerformanceReport();
        sb.append(String.format("Total Class Loads: %d\n", report.totalClassLoads));
        sb.append(String.format("Encrypted Class Loads: %d\n", report.encryptedClassLoads));
        sb.append(String.format("Failed Class Loads: %d\n", report.failedClassLoads));
        sb.append(String.format("Success Rate: %.2f%%\n", report.successRate));
        sb.append(String.format("Average Decryption Time: %.2fms\n", report.averageDecryptionTime));
        sb.append(String.format("Max Decryption Time: %dms\n", report.maxDecryptionTime));
        sb.append(String.format("Min Decryption Time: %dms\n", report.minDecryptionTime));
        sb.append(String.format("Cache Hit Rate: %.2f%%\n", report.cacheHitRate));
        sb.append(String.format("Slowest Class: %s\n", report.slowestClass));
        
        sb.append("\n=== Per-Class Statistics ===\n");
        classStats.forEach((className, stats) -> {
            sb.append(String.format("%s: loads=%d, avg=%.2fms, max=%dms\n", 
                    className, stats.loadCount, stats.averageTime, stats.maxTime));
        });
        
        return sb.toString();
    }
    
    /**
     * 重置统计信息
     */
    public void reset() {
        totalClassLoads.set(0);
        encryptedClassLoads.set(0);
        failedClassLoads.set(0);
        totalDecryptionTime.set(0);
        maxDecryptionTime.set(0);
        minDecryptionTime.set(Long.MAX_VALUE);
        cacheHits.set(0);
        cacheMisses.set(0);
        classStats.clear();
        slowestClass.set("");
        
        log.info("📊 [PERFORMANCE] Statistics reset");
    }
    
    private void updateMaxTime(long duration) {
        long current = maxDecryptionTime.get();
        while (duration > current && !maxDecryptionTime.compareAndSet(current, duration)) {
            current = maxDecryptionTime.get();
        }
    }
    
    private void updateMinTime(long duration) {
        long current = minDecryptionTime.get();
        while (duration < current && !minDecryptionTime.compareAndSet(current, duration)) {
            current = minDecryptionTime.get();
        }
    }
    
    private void updateClassStats(String className, long duration) {
        classStats.compute(className, (key, stats) -> {
            if (stats == null) {
                stats = new ClassLoadStats();
            }
            stats.loadCount++;
            stats.totalTime += duration;
            stats.averageTime = (double) stats.totalTime / stats.loadCount;
            stats.maxTime = Math.max(stats.maxTime, duration);
            return stats;
        });
    }
    
    private double calculateSuccessRate() {
        long total = totalClassLoads.get();
        if (total == 0) return 100.0;
        long failed = failedClassLoads.get();
        return ((double) (total - failed) / total) * 100.0;
    }
    
    private double calculateAverageDecryptionTime() {
        long loads = encryptedClassLoads.get();
        if (loads == 0) return 0.0;
        return (double) totalDecryptionTime.get() / loads;
    }
    
    private double calculateCacheHitRate() {
        long total = cacheHits.get() + cacheMisses.get();
        if (total == 0) return 0.0;
        return ((double) cacheHits.get() / total) * 100.0;
    }
    
    /**
     * 性能报告数据类
     */
    public static class PerformanceReport {
        public long totalClassLoads;
        public long encryptedClassLoads;
        public long failedClassLoads;
        public double successRate;
        public long totalDecryptionTime;
        public double averageDecryptionTime;
        public long maxDecryptionTime;
        public long minDecryptionTime;
        public long cacheHits;
        public long cacheMisses;
        public double cacheHitRate;
        public String slowestClass;
        public int classCount;
    }
    
    /**
     * 单个类的加载统计
     */
    private static class ClassLoadStats {
        int loadCount = 0;
        long totalTime = 0;
        double averageTime = 0.0;
        long maxTime = 0;
    }
}
