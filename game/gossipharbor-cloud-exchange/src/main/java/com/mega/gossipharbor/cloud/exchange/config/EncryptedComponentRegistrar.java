package com.mega.gossipharbor.cloud.exchange.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.core.Ordered;
import org.springframework.core.PriorityOrdered;
import org.springframework.stereotype.Component;

import com.mega.gossipharbor.cloud.exchange.security.EncryptedClassLoader;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 统一的加密组件注册器
 * 
 * 按正确顺序注册所有加密组件：
 * 1. 首先注册Dao接口（MyBatis Mapper）
 * 2. 然后注册Service类
 * 3. 最后注册其他组件
 */
@Slf4j
@Component
public class EncryptedComponentRegistrar implements BeanDefinitionRegistryPostProcessor, PriorityOrdered {

    private static final String ENCRYPTED_DIR = "encrypted";
    private static final String DAO_PACKAGE     = "com.mega.gossipharbor.cloud.exchange.secure.dao";
    private static final String SERVICE_PACKAGE = "com.mega.gossipharbor.cloud.exchange.secure.service";

    @Override
    public int getOrder() {
        // 设置最高优先级，确保在所有其他Bean创建之前执行
        return Ordered.HIGHEST_PRECEDENCE;
    }

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        log.info("🔧 [UNIFIED-REGISTRAR] Starting unified encrypted component registration...");
        
        try {
            // 1. 创建EncryptedClassLoader
            EncryptedClassLoader encryptedLoader = createEncryptedClassLoader();
            
            // 2. 按顺序注册组件
            registerDaoComponents(registry, encryptedLoader);
            registerServiceComponents(registry, encryptedLoader);
            
            log.info("✅ [UNIFIED-REGISTRAR] Unified encrypted component registration completed");
            
        } catch (Exception e) {
            log.error("❌ [UNIFIED-REGISTRAR] Failed to register encrypted components: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to register encrypted components", e);
        }
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        // 不需要实现
    }

    /**
     * 第一步：注册Dao组件（MyBatis Mapper）
     */
    private void registerDaoComponents(BeanDefinitionRegistry registry, EncryptedClassLoader classLoader) {
        log.info("🔍 [UNIFIED-REGISTRAR] Step 1: Registering Dao components...");
        
        List<String> daoClasses = scanEncryptedClasses(DAO_PACKAGE);
        
        for (String className : daoClasses) {
            registerDaoBean(registry, classLoader, className);
        }
        
        log.info("✅ [UNIFIED-REGISTRAR] Registered {} Dao components", daoClasses.size());
    }

    /**
     * 第二步：注册Service组件
     */
    private void registerServiceComponents(BeanDefinitionRegistry registry, EncryptedClassLoader classLoader) {
        log.info("🔍 [UNIFIED-REGISTRAR] Step 2: Registering Service components...");
        
        List<String> serviceClasses = scanEncryptedClasses(SERVICE_PACKAGE);
        
        for (String className : serviceClasses) {
            registerServiceBean(registry, classLoader, className);
        }
        
        log.info("✅ [UNIFIED-REGISTRAR] Registered {} Service components", serviceClasses.size());
    }

    /**
     * 注册单个Dao Bean（使用自定义EncryptedMapperFactoryBean支持加密XML）
     */
    private void registerDaoBean(BeanDefinitionRegistry registry, EncryptedClassLoader classLoader, String className) {
        try {
            log.info("🔐 [UNIFIED-REGISTRAR] Registering Dao: {}", className);

            // 使用EncryptedClassLoader加载接口
            Class<?> mapperInterface = classLoader.loadClass(className);

            // 验证是否为接口
            if (!mapperInterface.isInterface()) {
                log.warn("⚠️ [UNIFIED-REGISTRAR] Skipping non-interface class: {}", className);
                return;
            }

            // 生成Bean名称
            String beanName = generateBeanName(className);

            // 检查Bean是否已存在，如果存在则替换
            if (registry.containsBeanDefinition(beanName)) {
                log.warn("⚠️ [UNIFIED-REGISTRAR] Bean {} already exists, removing old definition", beanName);
                registry.removeBeanDefinition(beanName);
            }

            // 使用自定义的EncryptedMapperFactoryBean，支持加密XML文件
            BeanDefinition beanDefinition = BeanDefinitionBuilder
                    .genericBeanDefinition(EncryptedMapperFactoryBean.class)
                    .addPropertyValue("mapperInterface", mapperInterface)
                    .addPropertyValue("encryptedClassLoader", classLoader)
                    .setScope(BeanDefinition.SCOPE_SINGLETON)
                    .setAutowireMode(AbstractBeanDefinition.AUTOWIRE_BY_TYPE)
                    .setLazyInit(false)
                    .getBeanDefinition();

            // 注册到Spring容器
            registry.registerBeanDefinition(beanName, beanDefinition);

            log.info("✅ [UNIFIED-REGISTRAR] Successfully registered Dao: {} -> {}", beanName, className);

        } catch (Exception e) {
            log.error("❌ [UNIFIED-REGISTRAR] Failed to register Dao: {}", className, e);
            throw new RuntimeException("Failed to register Dao: " + className, e);
        }
    }

    /**
     * 注册单个Service Bean
     */
    private void registerServiceBean(BeanDefinitionRegistry registry, EncryptedClassLoader classLoader, String className) {
        try {
            log.info("🔐 [UNIFIED-REGISTRAR] Registering Service: {}", className);
            
            // 生成Bean名称
            String beanName = generateBeanName(className);
            
            // 检查Bean是否已存在，如果存在则替换
            if (registry.containsBeanDefinition(beanName)) {
                log.warn("⚠️ [UNIFIED-REGISTRAR] Bean {} already exists, removing old definition", beanName);
                registry.removeBeanDefinition(beanName);
            }
            
            // 使用自定义ClassLoader加载加密的类
            Class<?> encryptedClass = classLoader.loadClass(className);
            
            // 创建BeanDefinition
            BeanDefinition beanDefinition = BeanDefinitionBuilder
                    .genericBeanDefinition(encryptedClass)
                    .setScope(BeanDefinition.SCOPE_SINGLETON)
                    .setAutowireMode(AbstractBeanDefinition.AUTOWIRE_BY_TYPE)
                    .setLazyInit(false)
                    .getBeanDefinition();
            
            // 注册到Spring容器
            registry.registerBeanDefinition(beanName, beanDefinition);
            
            log.info("✅ [UNIFIED-REGISTRAR] Successfully registered Service: {} -> {}", beanName, className);

        } catch (Exception e) {
            log.error("❌ [UNIFIED-REGISTRAR] Failed to register Service: {}", className, e);
            throw new RuntimeException("Failed to register Service: " + className, e);
        }
    }

    /**
     * 扫描指定包下的加密类
     */
    private List<String> scanEncryptedClasses(String packageName) {
        List<String> classNames = new ArrayList<>();
        
        try {
            // 构建加密目录路径
            Path encryptedDir = Paths.get(ENCRYPTED_DIR);
            String packagePath = packageName.replace('.', File.separatorChar);
            Path packageDir = encryptedDir.resolve(packagePath);
            
            log.debug("🔍 [UNIFIED-REGISTRAR] Scanning directory: {}", packageDir.toAbsolutePath());
            
            File dir = packageDir.toFile();
            if (!dir.exists() || !dir.isDirectory()) {
                log.warn("⚠️ [UNIFIED-REGISTRAR] Directory not found: {}", packageDir);
                return classNames;
            }
            
            File[] files = dir.listFiles((file, name) -> name.endsWith(".class"));
            if (files != null) {
                for (File file : files) {
                    String className = convertPathToClassName(file.getAbsolutePath());
                    if (className != null) {
                        classNames.add(className);
                        log.debug("🔐 [UNIFIED-REGISTRAR] Found encrypted class: {}", className);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("❌ [UNIFIED-REGISTRAR] Failed to scan package {}: {}", packageName, e.getMessage(), e);
        }
        
        return classNames;
    }

    /**
     * 创建EncryptedClassLoader
     */
    private EncryptedClassLoader createEncryptedClassLoader() {
        try {
            // 使用正确的构造函数
            return new EncryptedClassLoader(new java.net.URL[0], Thread.currentThread().getContextClassLoader());
        } catch (Exception e) {
            throw new RuntimeException("Failed to create EncryptedClassLoader", e);
        }
    }

    /**
     * 将文件路径转换为类名
     */
    private String convertPathToClassName(String classPath) {
        try {
            // 找到com开始的位置
            int startIndex = classPath.indexOf("com" + File.separator);
            if (startIndex == -1) {
                return null;
            }
            
            // 提取从com开始的路径
            String relativePath = classPath.substring(startIndex);
            
            // 移除.class后缀
            if (relativePath.endsWith(".class")) {
                relativePath = relativePath.substring(0, relativePath.length() - 6);
            }
            
            // 将路径分隔符替换为点号
            return relativePath.replace(File.separator, ".");
            
        } catch (Exception e) {
            log.warn("⚠️ [UNIFIED-REGISTRAR] Failed to convert path to class name: {}", classPath, e);
            return null;
        }
    }

    /**
     * 生成Spring Bean名称
     */
    private String generateBeanName(String className) {
        // 提取简单类名
        String simpleName = className.substring(className.lastIndexOf('.') + 1);
        
        // 转换为驼峰命名（首字母小写）
        return Character.toLowerCase(simpleName.charAt(0)) + simpleName.substring(1);
    }
}
