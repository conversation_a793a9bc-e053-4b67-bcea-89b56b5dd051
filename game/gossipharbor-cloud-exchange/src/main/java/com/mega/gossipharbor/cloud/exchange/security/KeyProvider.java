package com.mega.gossipharbor.cloud.exchange.security;

/**
 * 密钥提供接口
 * 
 * 支持多种密钥源和密钥管理策略
 */
public interface KeyProvider {
    
    /**
     * 获取解密密钥
     * 
     * @return 解密密钥字符串
     * @throws KeyProviderException 密钥获取失败时抛出
     */
    String getDecryptionKey() throws KeyProviderException;
    
    /**
     * 刷新密钥（用于密钥轮换）
     * 
     * @throws KeyProviderException 密钥刷新失败时抛出
     */
    void refreshKey() throws KeyProviderException;
    
    /**
     * 验证密钥是否有效
     * 
     * @param key 要验证的密钥
     * @return 密钥是否有效
     */
    boolean isKeyValid(String key);
    
    /**
     * 获取密钥来源信息
     * 
     * @return 密钥来源描述
     */
    String getKeySource();
    
    /**
     * 检查密钥是否需要刷新
     * 
     * @return 是否需要刷新密钥
     */
    boolean needsRefresh();
}
