package com.mega.gossipharbor.cloud.exchange.secure.service;

import com.mega.gossipharbor.cloud.ExchangeException;
import com.mega.gossipharbor.cloud.common.vo.ExchangeTransactionReqVO;
import com.mega.gossipharbor.cloud.core.ds.DS;
import com.mega.gossipharbor.cloud.exchange.secure.dao.TransactionDao;
import com.mega.gossipharbor.cloud.exchange.secure.dto.ItemTurnoverRecord;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 交易服务类
 * 负责处理道具交易的核心业务逻辑
 */
@Service
@Slf4j
public class TransactionService {

    private final TransactionDao transactionDao;

    @Autowired
    public TransactionService(TransactionDao transactionDao) {
        this.transactionDao = transactionDao;
    }

    /**
     * 处理道具交易
     * @param reqVO 交易请求参数
     */
    @Transactional(rollbackFor = Exception.class)
    @DS("gameMaster")
    public void processTransaction(ExchangeTransactionReqVO reqVO) {
        log.info("开始处理道具交易，characterId={}, transactionId={}, gameRegionId={}", 
                reqVO.getCharacterId(), reqVO.getTransactionId(), reqVO.getGameRegionId());
        
        // 1. 库存检查：如果扣除列表不为空，先查询库存，库存不足直接抛出异常
        if (!CollectionUtils.isEmpty(reqVO.getReduceItemList())) {
            checkInventory(reqVO.getCharacterId(), reqVO.getReduceItemList());
        }

        // 2. 确保流水表存在
        String tableName = generateTableName(reqVO.getTimestamp());
        ensureTableExists(tableName);

        // 3. 收集所有流水记录
        List<ItemTurnoverRecord> turnoverRecords = new ArrayList<>();

        // 4. 处理增加道具
        if (!CollectionUtils.isEmpty(reqVO.getAddItemList())) {
            processItemChanges(reqVO.getCharacterId(), reqVO.getAddItemList(), reqVO.getTransactionId(), 
                             reqVO.getBusinessDicId(), reqVO.getTimestamp(), turnoverRecords, 1);
        }

        // 5. 处理减少道具
        if (!CollectionUtils.isEmpty(reqVO.getReduceItemList())) {
            processItemChanges(reqVO.getCharacterId(), reqVO.getReduceItemList(), reqVO.getTransactionId(), 
                             reqVO.getBusinessDicId(), reqVO.getTimestamp(), turnoverRecords, 2);
        }

        // 6. 批量插入流水记录
        if (!turnoverRecords.isEmpty()) {
            // 按月份分组批量插入（处理跨月情况）
            Map<String, List<ItemTurnoverRecord>> recordsByTable = turnoverRecords.stream()
                .collect(Collectors.groupingBy(record -> generateTableName(reqVO.getTimestamp())));
            
            for (Map.Entry<String, List<ItemTurnoverRecord>> entry : recordsByTable.entrySet()) {
                String tableNameForBatch = entry.getKey();
                List<ItemTurnoverRecord> records = entry.getValue();
                ensureTableExists(tableNameForBatch);
                transactionDao.batchInsertItemTurnover(tableNameForBatch, records);
                log.info("批量插入流水记录完成，表名={}, 记录数={}", tableNameForBatch, records.size());
            }
        }

        log.info("道具交易处理完成，characterId={}, transactionId={}", reqVO.getCharacterId(), reqVO.getTransactionId());
    }

    /**
     * 检查库存是否充足
     * @param characterId 角色ID
     * @param reduceItemList 扣除道具列表
     */
    private void checkInventory(Long characterId, List<ExchangeTransactionReqVO.ItemVO> reduceItemList) {
        for (ExchangeTransactionReqVO.ItemVO item : reduceItemList) {
            String itemCateCode = transactionDao.selectItemCateCodeByItemId(item.getItemId());
            if (itemCateCode == null) {
                throw new ExchangeException(5001); // 道具不存在
            }
            if (item.getItemNum() >= 0) {
                throw new ExchangeException(5004); // 扣除数量必须为负数
            }

            Long currentAmount = getCurrentAmount(characterId, item.getItemId(), itemCateCode);
            long requiredAmount = Math.abs(item.getItemNum()); // 扣除数量取绝对值

            if (currentAmount < requiredAmount) {
                log.warn("库存不足，characterId={}, itemId={}, currentAmount={}, requiredAmount={}", 
                        characterId, item.getItemId(), currentAmount, requiredAmount);
                throw new ExchangeException(5002); // 库存不足
            }
        }
    }

    /**
     * 处理道具变更
     * @param characterId 角色ID
     * @param itemList 道具列表
     * @param transactionId 事务ID
     * @param businessDicId 业务字典ID
     * @param timestamp 秒级别时间戳
     * @param turnoverRecords 流水记录收集列表
     * @param changeType 变更类型 (1=增加, 2=减少)
     */
    private void processItemChanges(Long characterId, List<ExchangeTransactionReqVO.ItemVO> itemList, 
                                   String transactionId, Long businessDicId, Long timestamp, 
                                   List<ItemTurnoverRecord> turnoverRecords, Integer changeType) {
        for (ExchangeTransactionReqVO.ItemVO item : itemList) {
            String itemCateCode = transactionDao.selectItemCateCodeByItemId(item.getItemId());
            if (itemCateCode == null) {
                throw new ExchangeException(5001); // 道具不存在
            }

            // 获取变更前数量
            // Long beforeAmount = getCurrentAmount(characterId, item.getItemId(), itemCateCode);
            // 获取变更前数量

            // 更新库存
            updateInventory(characterId, item.getItemId(), item.getItemNum(), itemCateCode);
            
            // // 获取变更后数量
            // Long afterAmount = getCurrentAmount(characterId, item.getItemId(), itemCateCode);

            // 收集流水记录
            LocalDateTime turnoverTime = Instant.ofEpochSecond(timestamp).atZone(ZoneOffset.systemDefault()).toLocalDateTime();
            // 转换为LocalDate
            LocalDate turnoverDate = turnoverTime.toLocalDate();

            ItemTurnoverRecord record = new ItemTurnoverRecord()
                .setCharacterId(characterId)
                .setItemId(item.getItemId())
                .setChangeType(changeType)
                .setChangeAmount(item.getItemNum())
                .setBusinessDicId((long) businessDicId)
                .setTransactionId(transactionId)
                .setItemUuid(item.getItemUuid())
                .setTurnoverTime(turnoverTime)
                .setTurnoverDate(turnoverDate);
            
            turnoverRecords.add(record);
        }
    }

    /**
     * 获取当前库存数量
     * @param characterId 角色ID
     * @param itemId 道具ID
     * @param itemCateCode 道具类型编码
     * @return 当前数量
     */
    private Long getCurrentAmount(Long characterId, Long itemId, String itemCateCode) {
        switch (itemCateCode.toUpperCase()) {
            case "CURRENCY":
                return transactionDao.selectCharacterCurrencyAmount(characterId, itemId);
            case "CONSUMABLE":
                return transactionDao.selectCharacterConsumableAmount(characterId, itemId);
            case "MATERIAL":
                return transactionDao.selectCharacterMaterialAmount(characterId, itemId);
            default:
                throw new ExchangeException(5003); // 不支持的道具类型
        }
    }

    /**
     * 更新库存
     * @param characterId 角色ID
     * @param itemId 道具ID
     * @param changeAmount 变更数量
     * @param itemCateCode 道具类型编码
     */
    private void updateInventory(Long characterId, Long itemId, Long changeAmount, 
                                String itemCateCode) {
        switch (itemCateCode.toUpperCase()) {
            case "CURRENCY":
                transactionDao.upsertCharacterCurrency(characterId, itemId, (long) changeAmount);
                break;
            case "CONSUMABLE":
                transactionDao.upsertCharacterConsumable(characterId, itemId, (long) changeAmount);
                break;
            case "MATERIAL":
                transactionDao.upsertCharacterMaterial(characterId, itemId, (long) changeAmount);
                break;
            default:
                throw new ExchangeException(5003); // 不支持的道具类型
        }
    }

    /**
     * 根据时间戳生成表名
     * @param timestamp 时间戳(秒)
     * @return 表名，如：item_turnover_202507
     */
    private String generateTableName(Long timestamp) {
        LocalDateTime dateTime = LocalDateTime.ofEpochSecond(timestamp, 0, ZoneOffset.UTC);
        String yearMonth = dateTime.format(DateTimeFormatter.ofPattern("yyyyMM"));
        return "item_turnover_" + yearMonth;
    }

    /**
     * 确保流水表存在，不存在则创建
     * @param tableName 表名
     */
    private void ensureTableExists(String tableName) {
        Integer exists = transactionDao.checkTableExists(tableName);
        if (exists == 0) {
            log.info("流水表不存在，开始创建：{}", tableName);
            transactionDao.createItemTurnoverTable(tableName);
            log.info("流水表创建完成：{}", tableName);
        }
    }


}