
package com.mega.gossipharbor.cloud.exchange.security;

import com.mega.gossipharbor.cloud.exchange.security.PerformanceMonitor;
import lombok.extern.slf4j.Slf4j;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 自定义类加载器，支持加密类的透明解密加载
 *
 * 功能特性:
 * 1. 继承URLClassLoader，保持与Spring Boot的兼容性
 * 2. 智能路由：加密类走解密路径，普通类走默认路径
 * 3. 类缓存：已解密的类缓存在内存中，提升性能
 * 4. 多种密钥源：支持系统属性、环境变量等多种密钥获取方式
 * 5. 线程安全：使用ConcurrentHashMap保证并发安全
 */
@Slf4j
public class EncryptedClassLoader extends URLClassLoader {

    private static final String ENCRYPTED_PACKAGE_PREFIX = "com.mega.gossipharbor.cloud.exchange.secure";

    private final EncryptedService encryptedService;
    private final PerformanceMonitor performanceMonitor;
    private final ConcurrentHashMap<String, Class<?>> classCache = new ConcurrentHashMap<>();
    private final boolean debugMode;

    public EncryptedClassLoader(URL[] urls, ClassLoader parent) {
        super(urls, parent);

        // 初始化调试模式
        this.debugMode = Boolean.parseBoolean(System.getProperty("encrypt.debug", "false"));

        // 初始化加密服务和性能监控
        this.encryptedService = EncryptedService.getInstance();
        this.performanceMonitor = new PerformanceMonitor();

        if (debugMode) {
            log.debug("EncryptedClassLoader initialized:");
            log.debug("  - Encrypted service: {}", encryptedService.getClass().getSimpleName());
            log.debug("  - Debug mode: enabled");
        }
    }

    @Override
    protected Class<?> findClass(String name) throws ClassNotFoundException {
        long startTime = performanceMonitor.recordClassLoadStart(name);

        if (debugMode) {
            log.debug("** Loading class: {}", name);
        }

        // 检查缓存
        Class<?> cachedClass = classCache.get(name);
        if (cachedClass != null) {
            if (debugMode) {
                log.debug("Class loaded from cache: {}", name);
            }
            performanceMonitor.recordEncryptedClassLoadComplete(name, startTime, true);
            return cachedClass;
        }

        // 判断是否为需要解密的类
        if (isEncryptedClass(name)) {
            try {
                Class<?> clazz = loadEncryptedClass(name);
                classCache.put(name, clazz);

                performanceMonitor.recordEncryptedClassLoadComplete(name, startTime, false);

                if (debugMode) {
                    long duration = System.currentTimeMillis() - startTime;
                    log.debug("Encrypted class loaded successfully: {} (took {}ms)", name, duration);
                }

                return clazz;
            } catch (Exception e) {
                performanceMonitor.recordClassLoadFailure(name, e);
                log.error("Failed to load encrypted class: {}", name, e);
                throw new ClassNotFoundException("Failed to load encrypted class: " + name, e);
            }
        }

        // 其他类使用默认加载方式
        try {
            Class<?> clazz = super.findClass(name);
            return clazz;
        } catch (ClassNotFoundException e) {
            performanceMonitor.recordClassLoadFailure(name, e);

            throw e;
        }
    }

    /**
     * 判断是否为需要解密的类
     */
    private boolean isEncryptedClass(String className) {
        // 首先检查包名前缀
        if (!className.startsWith(ENCRYPTED_PACKAGE_PREFIX)) {
            return false;
        }

        // 过滤掉Spring Boot自动配置相关的类名模式
        if (shouldSkipClass(className)) {
            if (debugMode) {
                log.debug("Skipping auto-generated class pattern: {}", className);
            }
            return false;
        }

        return true;
    }

    /**
     * 检查是否应该跳过某些类的加载
     * 这些类通常是Spring Boot自动配置机制尝试查找的，但实际不存在
     */
    private boolean shouldSkipClass(String className) {
        // 获取简单类名
        String simpleName = className.substring(className.lastIndexOf('.') + 1);

        // Spring Boot自动配置相关的后缀模式
        String[] skipSuffixes = {
            "Customizer",           // 配置定制器
            "Configuration",        // 配置类（如果不是我们实际定义的）
            "AutoConfiguration",    // 自动配置类
            "Properties",           // 配置属性类
            "Registrar",           // Bean注册器（如果不是我们实际定义的）
            "Selector",            // 导入选择器
            "Condition",           // 条件类
            "Advisor",             // AOP顾问
            "Interceptor",         // 拦截器（如果不是我们实际定义的）
            "Processor",           // 处理器（如果不是我们实际定义的）
            "Factory",             // 工厂类（如果不是我们实际定义的）
            "Builder",             // 构建器
            "Converter",           // 转换器
            "Resolver",            // 解析器
            "Handler",             // 处理器
            "Filter",              // 过滤器（如果不是我们实际定义的）
            "Listener",            // 监听器（如果不是我们实际定义的）
            "Aspect",              // 切面（如果不是我们实际定义的）
            "Advice"               // 通知
        };

        // 检查是否匹配跳过模式
        for (String suffix : skipSuffixes) {
            if (simpleName.endsWith(suffix)) {
                // 检查是否是我们实际定义的类（通过检查文件是否存在）
                if (!isActualEncryptedClassFile(className)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查是否存在实际的加密类文件
     */
    private boolean isActualEncryptedClassFile(String className) {
        return encryptedService.hasEncryptedClass(className);
    }

    /**
     * 加载并解密加密的类
     */
    private Class<?> loadEncryptedClass(String className) throws Exception {
        // 使用EncryptedService加载并解密类文件
        byte[] decryptedBytes = encryptedService.loadEncryptedClass(className);

        // 定义类
        return defineClass(className, decryptedBytes, 0, decryptedBytes.length);
    }

    /**
     * AES解密
     * 公共方法，供其他组件使用（如加密资源加载）
     * 现在委托给EncryptedService处理
     */
    public byte[] decrypt(byte[] encryptedData) throws Exception {
        return encryptedService.decrypt(encryptedData);
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        return String.format("Class cache: %d classes loaded", classCache.size());
    }

    /**
     * 获取性能统计信息
     */
    public String getPerformanceStats() {
        return performanceMonitor.getDetailedStats();
    }

    /**
     * 清空类缓存
     */
    public void clearCache() {
        classCache.clear();
        if (debugMode) {
            log.debug("Class cache cleared");
        }
    }
}
