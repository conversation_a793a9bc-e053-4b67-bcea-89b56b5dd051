package com.mega.gossipharbor.cloud.exchange.secure.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 道具流水记录实体
 * 用于批量插入流水表
 */
@Data
@Accessors(chain = true)
public class ItemTurnoverRecord {
    
    /**
     * 角色ID
     */
    private Long characterId;
    
    /**
     * 道具ID
     */
    private Long itemId;
    
    /**
     * 道具分类ID
     */
    private Long itemCateId;
    
    /**
     * 变更类型 (1=增加, 2=减少)
     */
    private Integer changeType;
    
    /**
     * 变更数量
     */
    private Long changeAmount;
    
    // /**
    //  * 变更前数量
    //  */
    // private Long beforeAmount;
    
    // /**
    //  * 变更后数量
    //  */
    // private Long afterAmount;
    
    /**
     * 业务字典ID
     */
    private Long businessDicId;
    
    /**
     * 交易ID
     */
    private String transactionId;
    
    /**
     * 道具UUID
     */
    private String itemUuid;
    
    /**
     * 创建时间
     */
    private LocalDateTime turnoverTime;
    
    /**
     * 创建日期
     */
    private LocalDate turnoverDate;
}