<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.gossipharbor.cloud.data.dao.GameDataDao">

    <!-- 插入或更新角色游戏数据 -->
    <insert id="insertOrUpdateGameData">
        INSERT INTO character_game_data (
            character_id,
            json_data,
            create_time,
            update_time,
            delsign
        ) VALUES (
            #{characterId},
            #{jsonData},
            NOW(),
            NOW(),
            0
        ) ON DUPLICATE KEY UPDATE
            json_data = VALUES(json_data),
            update_time = NOW()
    </insert>

    <!-- 插入游戏数据历史记录 -->
    <insert id="insertGameDataHistory">
        INSERT INTO character_game_data_history (
            character_id,
            json_data,
            create_time,
            update_time,
            delsign
        ) VALUES (
            #{characterId},
            #{jsonData},
            NOW(),
            NOW(),
            0
        )
    </insert>

    <!-- 根据角色ID查询游戏数据 -->
    <select id="selectGameDataByCharacterId" resultType="java.lang.String">
        SELECT json_data
        FROM character_game_data
        WHERE character_id = #{characterId}
        AND delsign = 0
    </select>

</mapper>
