package com.mega.gossipharbor.cloud.data.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 游戏数据访问层
 * 负责角色游戏数据的数据库操作
 */
@Mapper
public interface GameDataDao {

    /**
     * 插入或更新角色游戏数据
     * 使用 INSERT ... ON DUPLICATE KEY UPDATE 语法
     * 
     * @param characterId 角色ID
     * @param jsonData JSON格式游戏数据
     * @return 影响行数
     */
    int insertOrUpdateGameData(@Param("characterId") Long characterId, 
                              @Param("jsonData") String jsonData);

    /**
     * 插入游戏数据历史记录
     * 
     * @param characterId 角色ID
     * @param jsonData JSON格式游戏数据
     * @return 影响行数
     */
    int insertGameDataHistory(@Param("characterId") Long characterId, 
                             @Param("jsonData") String jsonData);

    /**
     * 根据角色ID查询游戏数据
     * 
     * @param characterId 角色ID
     * @return JSON格式游戏数据，如果不存在返回null
     */
    String selectGameDataByCharacterId(@Param("characterId") Long characterId);
}
