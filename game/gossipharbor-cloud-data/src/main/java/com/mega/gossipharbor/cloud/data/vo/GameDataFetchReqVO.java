package com.mega.gossipharbor.cloud.data.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 游戏数据获取请求VO
 */
@Data
@Accessors(chain = true)
@ApiModel("游戏数据获取请求")
public class GameDataFetchReqVO {

    @ApiModelProperty(value = "游戏大区ID", example = "1001")
    @NotNull(message = "游戏大区ID不能为空")
    private Long gameRegionId;

    @ApiModelProperty(value = "角色ID", example = "123456")
    @NotNull(message = "角色ID不能为空")
    private Long characterId;

    // @ApiModelProperty(value = "数据键", example = "playerData")
    // private String dataKey;
}
