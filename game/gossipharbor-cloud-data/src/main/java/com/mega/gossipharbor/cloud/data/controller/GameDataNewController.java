package com.mega.gossipharbor.cloud.data.controller;

import com.mega.gossipharbor.cloud.core.Result;
import com.mega.gossipharbor.cloud.core.Results;
import com.mega.gossipharbor.cloud.data.service.GameDataService;
import com.mega.gossipharbor.cloud.data.vo.GameDataFetchReqVO;
import com.mega.gossipharbor.cloud.data.vo.GameDataFetchRespVO;
import com.mega.gossipharbor.cloud.data.vo.GameDataSaveReqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 游戏数据控制器
 * 负责处理游戏数据存储和获取相关的API请求
 */
@RestController
@RequestMapping("/data/api/gamedata")
@Api(tags = "游戏数据管理(新版本)")
@Slf4j
public class GameDataNewController {

    private final GameDataService gameDataService;

    @Autowired
    public GameDataNewController(GameDataService gameDataService) {
        this.gameDataService = gameDataService;
    }

    /**
     * 存储游戏数据
     * 
     * @param reqVO 存储请求参数
     * @return 处理结果
     */
    @PostMapping("/save-data")
    @ApiOperation("存储游戏数据")
    public Result<?> saveGameDataNew(@Validated @RequestBody GameDataSaveReqVO reqVO) {
        log.info("收到游戏数据存储请求，characterId={}, gameRegionId={}", 
                reqVO.getCharacterId(), reqVO.getGameRegionId());
        
        gameDataService.saveGameData(reqVO);
        
        log.info("游戏数据存储完成，characterId={}", reqVO.getCharacterId());
        return Results.success();
    }

    /**
     * 获取游戏数据
     * 
     * @param reqVO 获取请求参数
     * @return 游戏数据响应
     */
    @PostMapping("/fetch-data")
    @ApiOperation("获取游戏数据")
    public Result<GameDataFetchRespVO> fetchGameDataNew(@Validated @RequestBody GameDataFetchReqVO reqVO) {
        log.info("收到游戏数据获取请求，characterId={}, gameRegionId={}", 
                reqVO.getCharacterId(), reqVO.getGameRegionId());
        
        GameDataFetchRespVO respVO = gameDataService.fetchGameData(reqVO);
        
        log.info("游戏数据获取完成，characterId={}", reqVO.getCharacterId());
        return Results.success(respVO);
    }
}
