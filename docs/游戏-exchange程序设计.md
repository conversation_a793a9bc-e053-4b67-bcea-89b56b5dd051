# exchange程序设计
## 设计文档
业务特点：
1. 高频的写入场景
2. 数据分析可以迁移到数据仓库（比如clickhouse）


提供唯一的路由，token校验是固定流程，入参如下:
路由路径: `/exchange/api/transaction`

入参:

* character_id 
* 业务渠道id int类型
* transaction_id varchar类型(订单号、交易号、掉落记录ID等)
* 增加道具列表 {道具id 道具类型 道具数量 特定道具-增加唯一uuid}
* 减少道具列表 {道具id 道具类型 道具数量} 
* 时间戳 发送者要求的时间


接口service实现：
1. 如果时扣除列表不为空,先查询库存,库存不足,直接抛出异常.
2. 开启事务
3. 增加物品时，先根据character+道具类型，写入用户的库存变，比如character_currency(角色货币库存表)
4. 再写入对应入参时间戳的流水表item_turnover(道具流水表)
5. 注意：写入库存和流水时，create_time使用入参的时间戳，update_time使用数据库时间。
6. 提交事务



数据库：
基础道具流水表item_turnover，按月做表的切片，提前建立3个月的。定时器检测未来3个月的表，是否存在，不存在新建。
写入时，根据入参时间戳，确定写入的item_turnover表名。


聚合层/数仓 (OLAP)： 
使用数据仓库 (如ClickHouse) ，定期（如每小时、每天）从基础流水表中抽取数据，按照分析需求预先聚合：
按道具类型、道具ID聚合 -> 生成经济分析宽表 
按业务渠道、业务子类型聚合 -> 生成业务分析宽表 
按玩家、按时间等多维度聚合。


## 流程图

### 主流程

```mermaid
flowchart TD
    A[客户端] --> B[发送Exchange请求]
    B --> C[Exchange服务接收请求]
    C --> D[Token验证子流程]
    D --> H{检查是否有扣除列表}
    H -->|无扣除列表| I[开启数据库事务]
    H -->|有扣除列表| J[查询当前库存]
    J --> K{库存是否充足}
    K -->|库存不足| L[抛出库存不足异常]
    K -->|库存充足| I
    I --> M[处理减少道具列表]
    M --> N[更新库存表item_currency]
    N --> O[处理增加道具列表]
    O --> P[更新库存表item_currency]
    P --> Q[合并所有流水记录为列表]
    Q --> R[根据时间戳确定流水表名]
    R --> S[批量插入流水表item_turnover_YYYYMM]
    S --> T{事务执行结果}
    T -->|成功| U[提交事务]
    T -->|失败| V[回滚事务]
    U --> W[返回成功结果]
    V --> X[返回失败结果]
    L --> Y[返回错误信息]
```



### 定时器创建数据库切片流程

```mermaid
flowchart TD
    A[定时器服务] --> B[检测未来3个月的流水表]
    B --> C{表是否存在}
    C -->|不存在| D[创建item_turnover_YYYYMM表]
    C -->|存在| E[继续检测下一个月]
    D --> E
    E --> F{是否检测完3个月}
    F -->|未完成| B
    F -->|完成| G[等待下次检测周期]
    G --> A
```

### OLAP聚合层ETL流程

```mermaid
flowchart TD
    A[ETL调度器] --> B[启动ETL任务]
    B --> C[检查数据源状态]
    C --> D{数据源是否可用}
    D -->|不可用| E[记录错误日志]
    D -->|可用| F[确定抽取时间范围]
    F --> G[从item_turnover_YYYYMM表抽取数据]
    G --> H[数据清洗和验证]
    H --> I{数据质量检查}
    I -->|质量不合格| J[数据质量告警]
    I -->|质量合格| K[数据转换处理]
    K --> L[按维度分组聚合]
    L --> M[生成经济分析宽表数据]
    L --> N[生成业务分析宽表数据]
    L --> O[生成玩家行为分析宽表数据]
    M --> P[写入ClickHouse经济分析表]
    N --> Q[写入ClickHouse业务分析表]
    O --> R[写入ClickHouse玩家行为表]
    P --> S[更新ETL元数据]
    Q --> S
    R --> S
    S --> T[ETL任务完成]
    E --> U[ETL任务失败]
    J --> U
```

### OLAP查询分析流程

```mermaid
flowchart TD
    A[分析查询请求] --> B[查询请求解析]
    B --> C[验证查询权限]
    C --> D{权限验证}
    D -->|权限不足| E[返回权限错误]
    D -->|权限通过| F[解析查询维度]
    F --> G{查询类型判断}
    G -->|经济分析| H[查询经济分析宽表]
    G -->|业务分析| I[查询业务分析宽表]
    G -->|玩家行为分析| J[查询玩家行为宽表]
    G -->|自定义查询| K[查询基础流水表]
    H --> L[执行ClickHouse查询]
    I --> L
    J --> L
    K --> M[执行MySQL查询]
    L --> N[查询结果处理]
    M --> N
    N --> O[数据格式化]
    O --> P[返回分析结果]
    E --> Q[查询失败]
```


## 时序图

### 主时序图

```mermaid
sequenceDiagram
    participant C as 客户端
    participant ES as Exchange服务
    participant TV as Token验证服务
    participant IS as 库存服务
    participant DB as 数据库
    
    C->>ES: 发送Exchange请求(character_id,业务渠道id,transaction_id,增加道具列表,减少道具列表,时间戳)
    ES->>ES: 调用Token验证子流程
    Note over ES,TV: 详见Token验证子时序图
    
    alt Token验证成功
        ES->>ES: 解析请求参数
        
        alt 有扣除列表
            ES->>IS: 查询当前库存
            IS->>DB: 查询库存数据
            DB-->>IS: 返回库存信息
            IS-->>ES: 返回库存结果
            
            alt 库存充足
                ES->>DB: 开启数据库事务
                DB-->>ES: 事务开启成功
                
                ES->>ES: 处理减少道具列表
                ES->>DB: 更新库存表(item_currency)
                DB-->>ES: 库存更新成功
                
                ES->>ES: 处理增加道具列表
                ES->>DB: 更新库存表(item_currency)
                DB-->>ES: 库存更新成功
                
                ES->>ES: 合并所有流水记录为列表
                ES->>ES: 根据时间戳确定流水表名
                ES->>DB: 批量插入流水表(item_turnover_YYYYMM)
                DB-->>ES: 流水记录成功
                
                ES->>DB: 提交事务
                DB-->>ES: 事务提交成功
                ES-->>C: 返回成功结果
            else 库存不足
                ES-->>C: 返回库存不足错误
            end
        else 无扣除列表
            ES->>DB: 开启数据库事务
            DB-->>ES: 事务开启成功
            
            ES->>ES: 处理增加道具列表
            ES->>DB: 更新库存表(item_currency)
            DB-->>ES: 库存更新成功
            
            ES->>ES: 合并所有流水记录为列表
            ES->>ES: 根据时间戳确定流水表名
            ES->>DB: 批量插入流水表(item_turnover_YYYYMM)
            DB-->>ES: 流水记录成功
            
            ES->>DB: 提交事务
            DB-->>ES: 事务提交成功
            ES-->>C: 返回成功结果
        end
    else Token验证失败
        ES-->>C: 返回认证错误
    end
```

### Token验证子时序图

```mermaid
sequenceDiagram
    participant ES as Exchange服务
    participant TV as Token验证服务
    participant AUTH as 认证中心
    participant CACHE as 缓存服务
    
    ES->>TV: 发送Token验证请求
    TV->>TV: 提取Token信息
    TV->>TV: 验证Token格式
    
    alt Token格式正确
        TV->>CACHE: 查询Token缓存
        alt 缓存命中且未过期
            CACHE-->>TV: 返回缓存的验证结果
            TV-->>ES: 返回验证成功
        else 缓存未命中或已过期
            TV->>AUTH: 验证Token有效性
            AUTH->>AUTH: 检查Token过期时间
            AUTH->>AUTH: 验证用户权限
            
            alt Token有效且权限充足
                AUTH-->>TV: 返回验证成功
                TV->>CACHE: 缓存验证结果
                TV-->>ES: 返回验证成功
            else Token无效或权限不足
                AUTH-->>TV: 返回验证失败
                TV-->>ES: 返回验证失败
            end
        end
    else Token格式错误
        TV-->>ES: 返回格式错误
    end
```

### 定时器时序图

```mermaid
sequenceDiagram
    participant TS as 定时器服务
    participant DB as 数据库
    
    Note over TS: 定时器服务独立运行
    loop 定期检测
        TS->>DB: 检测未来3个月的流水表是否存在
        alt 表不存在
            TS->>DB: 创建item_turnover_YYYYMM表
            DB-->>TS: 表创建成功
        else 表存在
            TS->>TS: 继续检测下一个月
        end
    end
```
