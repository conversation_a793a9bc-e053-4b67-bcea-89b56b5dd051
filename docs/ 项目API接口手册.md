# 📜 项目 API 接口手册

## 1. 项目概述

* **项目名称**：合合游戏 API
* **版本号**：v1.0.0
* **维护人**：
* **最后更新**：2025-07-26
* **文档版本**：v1.0.0

### 接口地址（Base URL）

| 环境   | 地址                                                             |
| ---- | -------------------------------------------------------------- |
| 生产环境 | [https://api.megagame.com](https://api.megagame.com)           |
| 测试环境 | [https://test-api.megagame.com](https://test-api.megagame.com) |
| 本地环境 | [http://localhost:8080](http://localhost:8080)                 |

---

## 2. 鉴权方式说明（Token）

* **认证方式**：使用 JWT Token，通过 HTTP Header 携带

* **请求头示例**：

  ```
  Authorization: Bearer {access_token}
  ```

* **Token 获取**：

    * 登录接口 `/account/api/auth/public/sms/login-or-register` 返回
    * 获取成功后，客户端需缓存并在后续请求中携带

* **Token 失效处理**：

    * Token 过期将返回 HTTP 401
    * 需要重新登录 `/account/api/auth/public/sms/login-or-register` 续签

---

## 3. 请求规则

### 3.1 请求格式

* Content-Type：`application/json`
* 请求方法：RESTful 风格，使用 GET / POST / PUT / DELETE

### 3.2 接口路径命名规范
>`{module}`为项目中模块后缀名称。  
> 例：  
> `gossipharbor-cloud-acount`模块`{module}`=`account`,   
> `gossipharbor-cloud-xx-xxx`模块`{module}`=`xx/xxx`
#### 3.2.1 不鉴权路径
```http request
/{module}/api/{controller}/public/{action}
```
示例路径：
```
/account/api/auth/public/sms/code
```
#### 3.2.2 鉴权路径
```
/{module}/api/{controller}/{action}
```
示例路径：
```
/data/api/gamedata/save-data
```
---

## 4. 响应结构约定

### 4.1 统一响应结构

```json
{
  "code": 0,
  "message": "success",
  "data": { }
}
```

### 4.2 字段说明

| 字段      | 类型     | 说明         |
| ------- | ------ | ---------- |
| code    | int    | 状态码，0 表示成功 |
| message | string | 提示信息       |
| data    | object | 实际业务数据     |

---

## 5. 通用错误码说明

| code | message          | 含义            |
| ---- | ---------------- | ------------- |
| 0    | success          | 成功            |
| 401  | unauthorized     | 未登录或 Token 过期 |
| 403  | forbidden        | 无权限           |
| 500  | internal\_error  | 服务器内部异常       |
| 1001 | invalid\_param   | 参数错误          |
| 1002 | business\_failed | 业务逻辑未通过       |

---

## 6. 通用参数定义

### 6.1 分页参数

| 参数名      | 类型  | 必填 | 说明   |
|----------| --- | -- | ---- |
| page  | int | 否  | 页码（Page Number） |
| pageSize | int | 否  | 每页条数（Page Size） |

### 6.2 排序参数

```
sort=字段名,asc|desc
```

---

## 7. 常用接口说明（非 Swagger 提供）

### 7.1 登录接口

* **地址**：`/account/api/auth/public/sms/login-or-register`
* **方法**：POST
* **说明**：用户登录，获取 access\_token

#### 请求参数

```json
{
  "areaCode": "+86",
  "phone": "***********",
  "verificationCode": "123456",
  "clientIp": "***********",
  "deviceId": "asdf123dfgdf123"
}
```

#### 响应参数

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "token": "xxx.yyy.zzz"
  }
}
```
---

## 8. Swagger 文档说明

* 在线地址：

    * 测试环境：[https://api-test.megagame.com/swagger-ui.html](https://api.megagame.com/swagger-ui.html)
    * 本地环境：[http://localhost:8080/swagger-ui/index.html](http://localhost:8080/swagger-ui/index.html)

### 使用说明

1. 打开 Swagger 页面
2. 点击右上角 `Authorize`
3. 输入：

   ```
   Bearer {access_token}
   ```
4. 选择接口，填写参数，点击 “Execute” 测试

---

## 9. 附录

### 9.1 时间格式说明

* 格式：`yyyy-MM-dd'T'HH:mm:ss.SSS'Z'`
* 示例：`2025-07-25T15:30:45.000Z`

### 9.2 建议工具

* 接口调试工具：Postman / Insomnia

---
