#  gossipharbor-cloud-data 接口需求

在 gossipharbor-cloud-data 模块中，要实现一些接口
controller目录新建`GameDataController.java`
service目录新建`GameDataService.java`
dao目录新建`GameDataDao.java` 并建立映射`GameDataDao.xml`、
vo目录新建`GameDataSaveReqVO.java`  `GameDataFetchReqVO.java` `GameDataFetchRespVO.java`


## GameDataController提供接口

### /api/game/save-data

接口说明：存储游戏数据
入参 GameDataSaveReqVO，包含字段：
* gameRegionId Long 游戏大区id 默认1001
* characterId  Long 角色id 测试阶段随便传
* jsonData     String  json格式数据
出参 无

### /api/game/fetch-data

接口说明：获取游戏数据
入参 GameDataFetchReqVO，包含字段：
* gameRegionId 游戏大区id
* characterId 角色id
* dataKey 数据key
出参 GameDataFetchRespVO，包含字段：
* jsonData     String  json格式数据


## 表结构

```sql
CREATE TABLE `character_game_data` (
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `json_data` json DEFAULT NULL COMMENT 'JSON格式的游戏数据',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记(0-未删除，1-已删除)',
  PRIMARY KEY (`character_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色游戏数据表';
```

```sql
CREATE TABLE `character_game_data_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增历史记录ID',
  `character_id` bigint(20) NOT NULL COMMENT '角色ID',
  `json_data` json DEFAULT NULL COMMENT 'JSON格式的游戏数据',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记(0-未删除，1-已删除)',
  PRIMARY KEY (`id`),
  KEY `idx_character_id` (`character_id`) COMMENT '角色ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色游戏数据历史记录表';
```


##  GameDataService 逻辑

save_data时执行：
* 写入表character_game_data，采用 insert into on duplicate key update 的方式
* 写入表character_game_data_history，采用 insert into 的方式

fetch_data时执行：
* 按character_id 查询表character_game_data，返回json_data
