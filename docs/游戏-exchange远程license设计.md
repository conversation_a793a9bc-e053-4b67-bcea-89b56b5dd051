## 远程服务access新增接口

### 1 从远程服务获得AES密钥
路由 `/access/public/partner/verify`

入参：
tokenKey: 
tokenSecret 
innerIp 内网ip
publicIp 公网ip
mac 机器地址


出参 aes


### 2 从请求远程服务验证license
路由 `/access/public/partner/license`
入参：
service_name: 
license:
innerIp:
publicIp:
mac

出参

错误直接推出程序


## 3 设计

在secure目录的DefaultKeyProvider类中，实现调用`/access/public/partner/verify` 获得aes密钥
在secure加密目录新增LicenseService, @PostConstruct启动，请求写死地址的`/access/public/license`的服务，根据返回码判断要不要推出游戏
